#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一的Action执行器

替换硬编码的action_router字典，提供配置驱动的action处理
"""

import importlib
import time
from typing import Dict, Any
import logging

from backend.config import config_service
from .base_action_handler import (
    BaseActionHandler,
    ActionHandlerRegistry,
    ActionContext,
    ActionResult,
    DefaultActionHandler
)
from .action_executor_interface import ActionExecutorInterface

logger = logging.getLogger(__name__)


class ActionExecutor(ActionExecutorInterface):
    """统一的Action执行器"""

    def __init__(self, conversation_flow):
        self.conversation_flow = conversation_flow
        self.registry = ActionHandlerRegistry()
        self.default_handler = DefaultActionHandler(conversation_flow)
        self.logger = logging.getLogger(self.__class__.__name__)

        # 注册默认处理器
        self.registry.register_handler(self.default_handler)

        # 加载配置的处理器
        self.load_handlers()

    def load_handlers(self):
        """加载所有处理器"""
        try:
            self.logger.info("[Action执行] 开始加载处理器...")
            # 先尝试从配置文件加载
            self._load_from_config()
            self.logger.info(f"[Action执行] 处理器加载完成，当前注册的处理器: {list(self.registry.handlers.keys())}")
            self.logger.info(f"[Action执行] 支持的actions: {self.registry.get_supported_actions()}")
        except Exception as e:
            self.logger.warning(f"[Action执行] 从配置文件加载处理器失败: {e}")
            # 回退到硬编码加载
            self._load_builtin_handlers()

    def _create_handler_instance(self, handler_name: str, handler_class: type) -> BaseActionHandler:
        """创建处理器实例，处理依赖注入"""
        if handler_name in ["conversation_handler", "ConversationHandler"]:
            # ConversationHandler 需要特定的依赖
            # 尝试从conversation_flow获取依赖，如果失败则从Factory获取
            try:
                # 首先尝试从conversation_flow获取依赖
                required_attrs = [
                    'db_manager', 'session_context_manager', 'message_manager',
                    'focus_point_manager', 'history_service'
                ]

                missing_attrs = []
                for attr in required_attrs:
                    if not hasattr(self.conversation_flow, attr) or getattr(self.conversation_flow, attr) is None:
                        missing_attrs.append(attr)

                if not missing_attrs:
                    # 从conversation_flow获取依赖
                    reply_factory = getattr(self.conversation_flow, 'reply_factory', None)
                    llm_client = getattr(self.conversation_flow, 'llm_client', None)

                    # 🔍 调试日志：检查conversation_flow中的组件状态
                    self.logger.info(f"[调试] conversation_flow.reply_factory: {reply_factory is not None} (类型: {type(reply_factory).__name__ if reply_factory else 'None'})")
                    self.logger.info(f"[调试] conversation_flow.llm_client: {llm_client is not None} (类型: {type(llm_client).__name__ if llm_client else 'None'})")

                    return handler_class(
                        db_manager=self.conversation_flow.db_manager,
                        session_context_manager=self.conversation_flow.session_context_manager,
                        message_manager=self.conversation_flow.message_manager,
                        focus_point_manager=self.conversation_flow.focus_point_manager,
                        history_service=self.conversation_flow.history_service,
                        reply_factory=reply_factory,
                        llm_client=llm_client
                    )
                else:
                    # 从Factory获取依赖
                    return self._create_conversation_handler_from_factory(handler_class)

            except Exception as e:
                self.logger.warning(f"从conversation_flow创建ConversationHandler失败: {e}")
                # 回退到Factory方式
                return self._create_conversation_handler_from_factory(handler_class)
        else:
            # 其他处理器使用默认的 conversation_flow 依赖
            return handler_class(self.conversation_flow)

    def _create_conversation_handler_from_factory(self, handler_class: type) -> BaseActionHandler:
        """从Factory获取依赖来创建ConversationHandler"""
        try:
            from backend.agents.factory import agent_factory
            from backend.agents.session_context import SessionContextManager
            from backend.data.db.message_manager import MessageManager
            from backend.data.db.focus_point_manager import FocusPointManager
            from backend.services.conversation_history_service import ConversationHistoryService

            # 从Factory获取依赖
            db_manager = agent_factory.get_database_manager()

            # 创建其他依赖
            session_context_manager = SessionContextManager(db_manager)
            message_manager = MessageManager(db_manager)
            focus_point_manager = FocusPointManager(db_manager)
            history_service = ConversationHistoryService(db_manager)

            return handler_class(
                db_manager=db_manager,
                session_context_manager=session_context_manager,
                message_manager=message_manager,
                focus_point_manager=focus_point_manager,
                history_service=history_service
            )
        except Exception as e:
            raise ValueError(f"ConversationHandler初始化失败：无法从Factory获取依赖 - {e}")

    def _load_from_config(self):
        """从配置文件加载处理器"""
        try:
            config = config_service.get_business_rule("action_handlers", {})

            if not config:
                self.logger.warning("[Action执行] 未找到action_handlers配置，使用内置处理器")
                self._load_builtin_handlers()
                return

            handler_classes = config.get("handler_classes", {})

            for handler_name, class_path in handler_classes.items():
                try:
                    handler_class = self._import_class(class_path)
                    handler_instance = self._create_handler_instance(handler_name, handler_class)
                    self.registry.register_handler(handler_instance)
                    self.logger.info(f"[Action执行] 成功加载处理器: {handler_name}")
                except Exception as e:
                    self.logger.error(f"[Action执行] 加载处理器 {handler_name} 失败: {e}")

        except Exception as e:
            self.logger.error(f"[Action执行] 加载配置文件失败: {e}")
            raise

    def _load_builtin_handlers(self):
        """加载内置处理器（回退方案）"""
        try:
            self.logger.info("[Action执行] 开始加载内置处理器...")

            # 加载会话处理器（使用新的依赖注入方式）
            from .conversation_handler import ConversationHandler

            try:
                # 验证conversation_flow的必需依赖
                required_attrs = [
                    'db_manager', 'session_context_manager', 'message_manager',
                    'focus_point_manager', 'history_service'
                ]

                missing_attrs = []
                for attr in required_attrs:
                    if not hasattr(self.conversation_flow, attr) or getattr(self.conversation_flow, attr) is None:
                        missing_attrs.append(attr)

                if missing_attrs:
                    raise ValueError(f"ConversationHandler依赖缺失: {missing_attrs}")

                # 从conversation_flow获取必要的服务
                reply_factory = getattr(self.conversation_flow, 'reply_factory', None)
                llm_client = getattr(self.conversation_flow, 'llm_client', None)

                # 🔍 调试日志：检查内置处理器加载时的组件状态
                self.logger.info(f"[调试] 内置处理器 - conversation_flow.reply_factory: {reply_factory is not None} (类型: {type(reply_factory).__name__ if reply_factory else 'None'})")
                self.logger.info(f"[调试] 内置处理器 - conversation_flow.llm_client: {llm_client is not None} (类型: {type(llm_client).__name__ if llm_client else 'None'})")

                conversation_handler = ConversationHandler(
                    db_manager=self.conversation_flow.db_manager,
                    session_context_manager=self.conversation_flow.session_context_manager,
                    message_manager=self.conversation_flow.message_manager,
                    focus_point_manager=self.conversation_flow.focus_point_manager,
                    history_service=self.conversation_flow.history_service,
                    reply_factory=reply_factory,
                    llm_client=llm_client
                )
                self.registry.register_handler(conversation_handler)
                self.logger.info("[Action执行] 成功加载会话处理器")
            except Exception as e:
                self.logger.error(f"[Action执行] 加载会话处理器失败: {e}")
                # 不抛出异常，继续加载其他处理器

            # 加载需求采集处理器
            from .requirement_handler import RequirementHandler
            requirement_handler = RequirementHandler(self.conversation_flow)
            self.registry.register_handler(requirement_handler)
            self.logger.info("[Action执行] 成功加载需求采集处理器")

            # 加载文档处理器
            from .document_handler import DocumentHandler
            document_handler = DocumentHandler(self.conversation_flow)
            self.registry.register_handler(document_handler)
            self.logger.info("[Action执行] 成功加载文档处理器")

            # 加载通用请求处理器
            from .general_request_handler import GeneralRequestHandler
            general_handler = GeneralRequestHandler(self.conversation_flow)
            self.registry.register_handler(general_handler)
            self.logger.info("[Action执行] 成功加载通用请求处理器")

            # 加载知识库处理器
            try:
                from .knowledge_base_handler import KnowledgeBaseHandler

                knowledge_handler = KnowledgeBaseHandler(
                    conversation_flow=self.conversation_flow,
                    rag_agent=self.rag_agent
                )
                self.registry.register_handler(knowledge_handler)
                self.logger.info("[Action执行] 成功加载知识库处理器")
            except Exception as e:
                self.logger.warning(f"[Action执行] 加载知识库处理器失败: {e}")

            # 加载复合意图处理器
            try:
                from .composite_handler import CompositeHandler

                # 尝试从conversation_flow获取rag_agent
                rag_agent = getattr(self.conversation_flow, 'rag_agent', None)

                composite_handler = CompositeHandler(
                    conversation_flow=self.conversation_flow,
                    rag_agent=rag_agent
                )
                self.registry.register_handler(composite_handler)
                self.logger.info("[Action执行] 成功加载复合意图处理器")
            except Exception as e:
                self.logger.warning(f"[Action执行] 加载复合意图处理器失败: {e}")

            self.logger.info("[Action执行] 成功加载所有内置处理器")
            self.logger.info(f"[Action执行] 内置处理器加载完成，当前注册的处理器: {list(self.registry.handlers.keys())}")
            self.logger.info(f"[Action执行] 支持的actions: {self.registry.get_supported_actions()}")

        except Exception as e:
            self.logger.error(f"[Action执行] 加载内置处理器失败: {e}", exc_info=True)
            # 即使内置处理器加载失败，也要确保有默认处理器可用

    def _import_class(self, class_path: str):
        """动态导入类"""
        module_path, class_name = class_path.rsplit('.', 1)
        module = importlib.import_module(module_path)
        return getattr(module, class_name)

    async def execute_action(self, action: str, context: ActionContext) -> ActionResult:
        """执行action"""
        start_time = time.time()

        try:
            self.logger.info(f"[Action执行] 执行action: {action}, session: {context.session_id}")

            # 添加调试信息：显示当前注册的处理器
            self.logger.debug(f"[Action执行] 当前注册的处理器: {list(self.registry.handlers.keys())}")
            self.logger.debug(f"[Action执行] action映射: {self.registry.action_mapping}")

            # 验证上下文
            if not await self._validate_context(context):
                return ActionResult(
                    success=False,
                    content="上下文验证失败",
                    error_message="Invalid context"
                )

            # 获取处理器
            handler = self.registry.get_handler(action)

            # 添加调试日志
            if handler:
                self.logger.debug(f"[Action执行] 找到处理器 {handler.get_handler_name()} for action: {action}")
                can_handle = await handler.can_handle(action, context)
                self.logger.debug(f"[Action执行] 处理器 {handler.get_handler_name()} can_handle({action}): {can_handle}")

                if can_handle:
                    self.logger.debug(f"[Action执行] 使用处理器 {handler.get_handler_name()} 处理action: {action}")
                    result = await handler.handle(context)
                else:
                    self.logger.warning(f"[Action执行] 处理器 {handler.get_handler_name()} 无法处理action: {action}，使用默认处理器")
                    result = await self.default_handler.handle(context)
            else:
                self.logger.warning(f"[Action执行] 未找到action '{action}' 的处理器，使用默认处理器")
                self.logger.debug(f"[Action执行] 当前支持的actions: {self.registry.get_supported_actions()}")
                result = await self.default_handler.handle(context)

            # 记录执行时间
            execution_time = time.time() - start_time
            result.metadata["execution_time"] = execution_time
            result.metadata["handler_used"] = handler.get_handler_name() if handler else "DefaultHandler"

            self.logger.info(f"[Action执行] Action {action} 执行完成，耗时: {execution_time:.3f}s")
            return result

        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"[Action执行] 执行action {action} 失败: {e}", exc_info=True)

            return ActionResult(
                success=False,
                content="系统处理您的请求时遇到问题，请稍后再试。",
                error_message=str(e),
                metadata={
                    "execution_time": execution_time,
                    "error_type": type(e).__name__
                }
            )

    async def _validate_context(self, context: ActionContext) -> bool:
        """验证执行上下文"""
        if not context.action:
            self.logger.error("[Action执行] Action不能为空")
            return False

        if not context.session_id:
            self.logger.error("[Action执行] Session ID不能为空")
            return False

        if not context.conversation_flow:
            self.logger.error("[Action执行] ConversationFlow实例不能为空")
            return False

        return True

    def get_supported_actions(self) -> list:
        """获取所有支持的actions"""
        return self.registry.get_supported_actions()

    def get_handler_info(self) -> Dict[str, Any]:
        """获取处理器信息"""
        handlers_info = {}
        for name, handler in self.registry.get_all_handlers().items():
            handlers_info[name] = {
                "class": handler.__class__.__name__,
                "supported_actions": handler.supported_actions
            }
        return handlers_info

    def reload_handlers(self):
        """重新加载处理器"""
        self.logger.info("[Action执行] 重新加载处理器")

        # 清空现有处理器（保留默认处理器）
        self.registry.clear()
        self.registry.register_handler(self.default_handler)

        # 重新加载
        self.load_handlers()

        self.logger.info("[Action执行] 处理器重新加载完成")
