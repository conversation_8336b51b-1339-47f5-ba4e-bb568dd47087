#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Action处理器基础架构

提供统一的Action处理接口，实现配置驱动的架构设计
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional
import logging
from backend.config import config_service

logger = logging.getLogger(__name__)


@dataclass
class ActionContext:
    """Action执行上下文"""
    action: str
    message: str
    session_id: str
    user_id: str
    decision_result: Dict[str, Any]
    history: List[Dict[str, Any]]
    current_state: str
    current_domain: str
    current_category: str
    conversation_flow: Any  # ConversationFlow实例
    emotion: str = "neutral"  # 用户情绪状态
    additional_data: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ActionResult:
    """Action执行结果"""
    success: bool
    content: str
    next_state: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None


class BaseActionHandler(ABC):
    """Action处理器基类"""

    def __init__(self, conversation_flow=None):
        self.conversation_flow = conversation_flow
        self.logger = logging.getLogger(self.__class__.__name__)

    @property
    @abstractmethod
    def supported_actions(self) -> List[str]:
        """返回支持的action列表"""

    @abstractmethod
    async def can_handle(self, action: str, context: ActionContext) -> bool:
        """判断是否能处理指定的action"""

    @abstractmethod
    async def handle(self, context: ActionContext) -> ActionResult:
        """处理action"""

    def get_handler_name(self) -> str:
        """获取处理器名称"""
        return self.__class__.__name__

    async def validate_context(self, context: ActionContext) -> bool:
        """验证上下文是否有效"""
        if not context.action:
            self.logger.error("Action不能为空")
            return False

        if not context.session_id:
            self.logger.error("Session ID不能为空")
            return False

        return True

    def create_success_result(self, content: str, next_state: str = None, **metadata) -> ActionResult:
        """创建成功结果"""
        return ActionResult(
            success=True,
            content=content,
            next_state=next_state,
            metadata=metadata
        )

    def create_error_result(self, error_message: str, content: str = None) -> ActionResult:
        """创建错误结果"""
        return ActionResult(
            success=False,
            content=content or f"处理失败: {error_message}",
            error_message=error_message
        )


class ActionHandlerRegistry:
    """Action处理器注册器"""

    def __init__(self):
        self.handlers: Dict[str, BaseActionHandler] = {}
        self.action_mapping: Dict[str, str] = {}
        self.logger = logging.getLogger(self.__class__.__name__)

    def register_handler(self, handler: BaseActionHandler):
        """注册处理器"""
        handler_name = handler.get_handler_name()
        self.handlers[handler_name] = handler

        # 注册支持的actions
        for action in handler.supported_actions:
            if action in self.action_mapping:
                self.logger.warning(f"Action '{action}' 已被 {self.action_mapping[action]} 处理器注册，将被 {handler_name} 覆盖")
            self.action_mapping[action] = handler_name

        self.logger.info(f"注册处理器 {handler_name}，支持actions: {handler.supported_actions}")

    def get_handler(self, action: str) -> Optional[BaseActionHandler]:
        """获取处理器"""
        handler_name = self.action_mapping.get(action)
        if handler_name:
            return self.handlers.get(handler_name)
        return None

    def get_all_handlers(self) -> Dict[str, BaseActionHandler]:
        """获取所有处理器"""
        return self.handlers.copy()

    def get_supported_actions(self) -> List[str]:
        """获取所有支持的actions"""
        return list(self.action_mapping.keys())

    def unregister_handler(self, handler_name: str):
        """注销处理器"""
        if handler_name in self.handlers:
            handler = self.handlers[handler_name]
            # 移除action映射
            for action in handler.supported_actions:
                if self.action_mapping.get(action) == handler_name:
                    del self.action_mapping[action]

            del self.handlers[handler_name]
            self.logger.info(f"注销处理器 {handler_name}")

    def clear(self):
        """清空所有处理器"""
        self.handlers.clear()
        self.action_mapping.clear()
        self.logger.info("清空所有处理器")


class DefaultActionHandler(BaseActionHandler):
    """默认Action处理器，处理未知的action"""

    @property
    def supported_actions(self) -> List[str]:
        return ["*"]  # 支持所有action

    async def can_handle(self, action: str, context: ActionContext) -> bool:
        return True  # 总是可以处理（作为兜底）

    async def handle(self, context: ActionContext) -> ActionResult:
        """处理未知action"""
        self.logger.warning(f"处理未知action: {context.action}")

        # 尝试调用conversation_flow的handle_unknown_action方法
        if self.conversation_flow and hasattr(self.conversation_flow, 'handle_unknown_action'):
            try:
                result = await self.conversation_flow.handle_unknown_action(
                    message=context.message,
                    session_id=context.session_id,
                    decision_result=context.decision_result
                )
                return self.create_success_result(result)
            except Exception as e:
                self.logger.error(f"调用handle_unknown_action失败: {e}")

        # 兜底回复
        
        fallback_message = config.get_message_template("error.general_fallback")
        return self.create_success_result(fallback_message)
