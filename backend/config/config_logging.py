#!/usr/bin/env python3
"""
配置降级与来源日志规范

定义统一的配置变更、降级、覆盖日志格式，包括：
1. 配置来源追踪
2. 配置值变更记录
3. 敏感信息脱敏
4. 降级事件记录
5. 覆盖事件记录

日志格式标准化，便于监控和审计。
"""

import logging
import json
import re
from typing import Any, Dict, Optional, List, Union
from enum import Enum
from dataclasses import dataclass, asdict
from datetime import datetime

logger = logging.getLogger(__name__)


class ConfigEventType(Enum):
    """配置事件类型"""
    LOAD = "load"           # 配置加载
    OVERRIDE = "override"   # 配置覆盖
    FALLBACK = "fallback"   # 降级兜底
    MERGE = "merge"         # 配置合并
    VALIDATION = "validation"  # 配置验证
    ERROR = "error"         # 配置错误


class ConfigSource(Enum):
    """配置来源类型"""
    HARDCODED = "hardcoded"     # 硬编码
    DEFAULTS = "defaults"       # 默认文件
    CONFIG_FILE = "config_file" # 配置文件
    ENV_VAR = "env_var"        # 环境变量
    RUNTIME = "runtime"        # 运行时设置
    UNKNOWN = "unknown"        # 未知来源


@dataclass
class ConfigEvent:
    """配置事件数据结构"""
    timestamp: str
    event_type: ConfigEventType
    config_key: str
    source: ConfigSource
    old_value: Optional[Any] = None
    new_value: Optional[Any] = None
    source_detail: Optional[str] = None  # 具体来源（如文件路径、环境变量名）
    reason: Optional[str] = None         # 事件原因
    is_sensitive: bool = False           # 是否为敏感配置
    validation_result: Optional[bool] = None  # 验证结果
    error_message: Optional[str] = None  # 错误信息


class ConfigLogger:
    """配置日志记录器
    
    提供统一的配置变更、降级、覆盖日志记录功能
    """
    
    # 敏感配置键模式（正则表达式）
    SENSITIVE_PATTERNS = [
        r'.*password.*',
        r'.*secret.*',
        r'.*key.*',
        r'.*token.*',
        r'.*credential.*',
        r'.*auth.*',
        r'.*api_key.*',
        r'.*private.*'
    ]
    
    def __init__(self, logger_name: str = "config_events"):
        self.logger = logging.getLogger(logger_name)
        self._sensitive_regex = [re.compile(pattern, re.IGNORECASE) 
                                for pattern in self.SENSITIVE_PATTERNS]
    
    def _is_sensitive_key(self, config_key: str) -> bool:
        """判断配置键是否敏感"""
        return any(regex.match(config_key) for regex in self._sensitive_regex)
    
    def _mask_sensitive_value(self, value: Any, is_sensitive: bool = False) -> Any:
        """脱敏敏感值"""
        if not is_sensitive or value is None:
            return value
            
        if isinstance(value, str):
            if len(value) <= 4:
                return "*" * len(value)
            else:
                # 保留前2位和后2位，中间用*替换
                return value[:2] + "*" * (len(value) - 4) + value[-2:]
        elif isinstance(value, (int, float)):
            return "***"
        elif isinstance(value, (dict, list)):
            return f"<{type(value).__name__} with {len(value)} items>"
        else:
            return f"<{type(value).__name__}>"
    
    def _format_log_message(self, event: ConfigEvent) -> str:
        """格式化日志消息"""
        # 基础消息
        msg_parts = [
            f"[{event.event_type.value.upper()}]",
            f"key={event.config_key}",
            f"source={event.source.value}"
        ]
        
        # 添加来源详情
        if event.source_detail:
            msg_parts.append(f"source_detail={event.source_detail}")
        
        # 添加值变更信息
        if event.old_value is not None or event.new_value is not None:
            old_val = self._mask_sensitive_value(event.old_value, event.is_sensitive)
            new_val = self._mask_sensitive_value(event.new_value, event.is_sensitive)
            msg_parts.append(f"change: {old_val} -> {new_val}")
        
        # 添加原因
        if event.reason:
            msg_parts.append(f"reason={event.reason}")
        
        # 添加验证结果
        if event.validation_result is not None:
            msg_parts.append(f"valid={event.validation_result}")
        
        # 添加错误信息
        if event.error_message:
            msg_parts.append(f"error={event.error_message}")
        
        return " | ".join(msg_parts)
    
    def _get_structured_log_data(self, event: ConfigEvent) -> Dict[str, Any]:
        """获取结构化日志数据"""
        data = asdict(event)
        
        # 脱敏敏感值
        if event.is_sensitive:
            data['old_value'] = self._mask_sensitive_value(event.old_value, True)
            data['new_value'] = self._mask_sensitive_value(event.new_value, True)
        
        # 转换枚举为字符串
        data['event_type'] = event.event_type.value
        data['source'] = event.source.value
        
        return data
    
    def log_config_load(self, config_key: str, value: Any, source: ConfigSource, 
                       source_detail: Optional[str] = None):
        """记录配置加载事件"""
        is_sensitive = self._is_sensitive_key(config_key)
        
        event = ConfigEvent(
            timestamp=datetime.now().isoformat(),
            event_type=ConfigEventType.LOAD,
            config_key=config_key,
            source=source,
            new_value=value,
            source_detail=source_detail,
            is_sensitive=is_sensitive
        )
        
        self._log_event(event, logging.INFO)
    
    def log_config_override(self, config_key: str, old_value: Any, new_value: Any,
                           source: ConfigSource, source_detail: Optional[str] = None,
                           reason: Optional[str] = None):
        """记录配置覆盖事件"""
        is_sensitive = self._is_sensitive_key(config_key)
        
        event = ConfigEvent(
            timestamp=datetime.now().isoformat(),
            event_type=ConfigEventType.OVERRIDE,
            config_key=config_key,
            source=source,
            old_value=old_value,
            new_value=new_value,
            source_detail=source_detail,
            reason=reason,
            is_sensitive=is_sensitive
        )
        
        self._log_event(event, logging.WARNING)
    
    def log_config_fallback(self, config_key: str, fallback_value: Any,
                           source: ConfigSource, reason: str,
                           source_detail: Optional[str] = None):
        """记录配置降级兜底事件"""
        is_sensitive = self._is_sensitive_key(config_key)
        
        event = ConfigEvent(
            timestamp=datetime.now().isoformat(),
            event_type=ConfigEventType.FALLBACK,
            config_key=config_key,
            source=source,
            new_value=fallback_value,
            source_detail=source_detail,
            reason=reason,
            is_sensitive=is_sensitive
        )
        
        self._log_event(event, logging.WARNING)
    
    def log_config_merge(self, config_key: str, sources: List[str], 
                        final_value: Any, reason: Optional[str] = None):
        """记录配置合并事件"""
        is_sensitive = self._is_sensitive_key(config_key)
        
        event = ConfigEvent(
            timestamp=datetime.now().isoformat(),
            event_type=ConfigEventType.MERGE,
            config_key=config_key,
            source=ConfigSource.UNKNOWN,
            new_value=final_value,
            source_detail=f"merged from: {', '.join(sources)}",
            reason=reason,
            is_sensitive=is_sensitive
        )
        
        self._log_event(event, logging.INFO)
    
    def log_config_validation(self, config_key: str, value: Any, 
                             validation_result: bool, error_message: Optional[str] = None):
        """记录配置验证事件"""
        is_sensitive = self._is_sensitive_key(config_key)
        
        event = ConfigEvent(
            timestamp=datetime.now().isoformat(),
            event_type=ConfigEventType.VALIDATION,
            config_key=config_key,
            source=ConfigSource.UNKNOWN,
            new_value=value,
            validation_result=validation_result,
            error_message=error_message,
            is_sensitive=is_sensitive
        )
        
        level = logging.ERROR if not validation_result else logging.INFO
        self._log_event(event, level)
    
    def log_config_error(self, config_key: str, error_message: str,
                        source: Optional[ConfigSource] = None,
                        source_detail: Optional[str] = None):
        """记录配置错误事件"""
        event = ConfigEvent(
            timestamp=datetime.now().isoformat(),
            event_type=ConfigEventType.ERROR,
            config_key=config_key,
            source=source or ConfigSource.UNKNOWN,
            source_detail=source_detail,
            error_message=error_message,
            is_sensitive=self._is_sensitive_key(config_key)
        )
        
        self._log_event(event, logging.ERROR)
    
    def _log_event(self, event: ConfigEvent, level: int):
        """记录事件到日志"""
        # 格式化消息
        message = self._format_log_message(event)
        
        # 结构化数据
        structured_data = self._get_structured_log_data(event)
        
        # 记录日志
        self.logger.log(level, message, extra={
            "config_event": structured_data,
            "event_type": event.event_type.value,
            "config_key": event.config_key,
            "source": event.source.value,
            "is_sensitive": event.is_sensitive
        })


# ============================================================================
# 全局配置日志记录器
# ============================================================================

# 创建全局配置日志记录器实例
config_logger = ConfigLogger()

def get_config_logger() -> ConfigLogger:
    """获取配置日志记录器实例"""
    return config_logger

# ============================================================================
# 便捷函数
# ============================================================================

def log_config_load(config_key: str, value: Any, source: str, source_detail: Optional[str] = None):
    """便捷函数：记录配置加载"""
    source_enum = ConfigSource(source) if source in [s.value for s in ConfigSource] else ConfigSource.UNKNOWN
    config_logger.log_config_load(config_key, value, source_enum, source_detail)

def log_config_override(config_key: str, old_value: Any, new_value: Any, 
                       source: str, reason: Optional[str] = None):
    """便捷函数：记录配置覆盖"""
    source_enum = ConfigSource(source) if source in [s.value for s in ConfigSource] else ConfigSource.UNKNOWN
    config_logger.log_config_override(config_key, old_value, new_value, source_enum, reason=reason)

def log_config_fallback(config_key: str, fallback_value: Any, reason: str):
    """便捷函数：记录配置降级"""
    config_logger.log_config_fallback(config_key, fallback_value, ConfigSource.HARDCODED, reason)
