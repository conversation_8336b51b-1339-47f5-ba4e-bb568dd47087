#!/usr/bin/env python3
"""
模块化配置加载器

实现分文件配置的加载、聚合和缓存功能，支持：
1. 多配置源聚合（分文件 + 环境变量 + 默认值）
2. 优先级覆盖策略
3. 配置缓存和热更新
4. 类型安全和验证
5. 性能监控

设计原则：
- 单一门面：对外提供统一的配置访问接口
- 分而治之：内部支持多配置源管理
- 聚合统一：运行时聚合为只读快照
- 观测先行：全链路监控和日志
"""

import os
import yaml
import logging
import threading
import time
from typing import Any, Dict, Optional, List, Callable
from pathlib import Path
from dataclasses import dataclass, field
from datetime import datetime
from copy import deepcopy

logger = logging.getLogger(__name__)

# 导入配置日志记录器
try:
    from .config_logging import get_config_logger, ConfigSource as LogConfigSource
    config_logger = get_config_logger()
except ImportError:
    config_logger = None
    LogConfigSource = None


@dataclass
class ConfigSource:
    """配置源定义"""
    name: str
    file_path: Path
    priority: int
    config_data: Dict[str, Any] = field(default_factory=dict)
    last_modified: float = 0
    load_time: float = 0
    error_count: int = 0
    change_listeners: List[Callable] = field(default_factory=list)

    def __post_init__(self):
        """初始化后加载配置"""
        self._load_initial_config()

    def _load_initial_config(self):
        """加载初始配置"""
        start_time = time.time()
        try:
            if self.file_path.exists():
                self.last_modified = self.file_path.stat().st_mtime
                with open(self.file_path, 'r', encoding='utf-8') as f:
                    raw_config = yaml.safe_load(f) or {}

                # 解析环境变量
                logger.info(f"配置源 {self.name} 开始解析环境变量")
                self.config_data = self._resolve_env_variables(raw_config)
                logger.info(f"配置源 {self.name} 环境变量解析完成")
                logger.debug(f"配置源 {self.name} 加载成功: {len(self.config_data)} 项")

                # 记录配置加载日志
                if config_logger and LogConfigSource:
                    config_logger.log_config_load(
                        self.name, f"{len(self.config_data)} items",
                        LogConfigSource.CONFIG_FILE, str(self.file_path)
                    )
            else:
                self.config_data = {}
                logger.warning(f"配置文件不存在: {self.file_path}")

                # 记录配置文件缺失日志
                if config_logger and LogConfigSource:
                    config_logger.log_config_error(
                        self.name, f"配置文件不存在: {self.file_path}",
                        LogConfigSource.CONFIG_FILE, str(self.file_path)
                    )
        except Exception as e:
            self.error_count += 1
            logger.error(f"配置源 {self.name} 加载失败: {e}")
            self.config_data = {}

            # 记录配置加载错误日志
            if config_logger and LogConfigSource:
                config_logger.log_config_error(
                    self.name, f"配置加载失败: {str(e)}",
                    LogConfigSource.CONFIG_FILE, str(self.file_path)
                )
        finally:
            self.load_time = time.time() - start_time

    def _resolve_env_variables(self, config):
        """递归解析配置中的环境变量占位符"""
        import os
        import re

        if isinstance(config, dict):
            return {key: self._resolve_env_variables(value) for key, value in config.items()}
        elif isinstance(config, list):
            return [self._resolve_env_variables(item) for item in config]
        elif isinstance(config, str):
            # 匹配 ${VAR_NAME} 格式的环境变量
            def replace_env_var(match):
                var_name = match.group(1)
                env_value = os.getenv(var_name)
                if env_value is None:
                    logger.warning(f"配置源 {self.name}: 环境变量 {var_name} 未设置，保持原值")
                    return match.group(0)  # 返回原始占位符
                else:
                    logger.info(f"配置源 {self.name}: 环境变量 {var_name} 解析成功")
                    return env_value

            original_config = config
            resolved_config = re.sub(r'\$\{([^}]+)\}', replace_env_var, config)
            if original_config != resolved_config:
                logger.info(f"配置源 {self.name}: 字符串环境变量解析: '{original_config}' -> '{resolved_config[:50]}...'")
            return resolved_config
        else:
            return config

    def reload(self) -> bool:
        """重新加载配置"""
        if not self.file_path.exists():
            return False

        try:
            new_modified = self.file_path.stat().st_mtime
            if new_modified > self.last_modified:
                old_config = deepcopy(self.config_data)
                self._load_initial_config()

                # 通知变更监听器
                for listener in self.change_listeners:
                    try:
                        listener(self.name, old_config, self.config_data)
                    except Exception as e:
                        logger.error(f"配置变更通知失败 {self.name}: {e}")

                return True
        except Exception as e:
            self.error_count += 1
            logger.error(f"配置源 {self.name} 重载失败: {e}")

        return False

    def add_change_listener(self, listener: Callable):
        """添加变更监听器"""
        self.change_listeners.append(listener)


@dataclass
class ConfigSnapshot:
    """配置快照"""
    version: str
    timestamp: datetime
    config_data: Dict[str, Any]
    source_info: Dict[str, Any]
    checksum: str = ""

    def __post_init__(self):
        """计算校验和"""
        import hashlib
        import json
        content = json.dumps(self.config_data, sort_keys=True)
        self.checksum = hashlib.md5(content.encode()).hexdigest()


class ConfigMonitoring:
    """配置监控"""

    def __init__(self):
        self.access_count = 0
        self.cache_hits = 0
        self.cache_misses = 0
        self.reload_count = 0
        self.error_count = 0
        self.start_time = time.time()

    def record_access(self, cache_hit: bool = False):
        """记录访问"""
        self.access_count += 1
        if cache_hit:
            self.cache_hits += 1
        else:
            self.cache_misses += 1

    def record_reload(self):
        """记录重载"""
        self.reload_count += 1

    def record_error(self):
        """记录错误"""
        self.error_count += 1

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        uptime = time.time() - self.start_time
        cache_hit_rate = self.cache_hits / max(self.access_count, 1)

        return {
            "uptime": uptime,
            "access_count": self.access_count,
            "cache_hit_rate": cache_hit_rate,
            "reload_count": self.reload_count,
            "error_count": self.error_count,
            "error_rate": self.error_count / max(self.access_count, 1)
        }


class ModularConfigLoader:
    """模块化配置加载器"""

    def __init__(self, config_dir: str = "backend/config", enabled: bool = False):
        self.config_dir = Path(config_dir)
        self.enabled = enabled  # 特性开关，默认关闭
        self.config_sources: Dict[str, ConfigSource] = {}
        self.config_cache: Dict[str, Any] = {}
        self.cache_lock = threading.RLock()
        self.current_snapshot: Optional[ConfigSnapshot] = None
        self.monitoring = ConfigMonitoring()

        # 托底配置（硬编码默认值）
        self.default_config = self._load_default_config()

        # 只有在启用时才初始化
        if self.enabled:
            self._initialize_sources()
            logger.info("模块化配置加载器已启用")
        else:
            logger.info("模块化配置加载器已禁用，使用传统加载方式")

    def _load_default_config(self) -> Dict[str, Any]:
        """加载代码级托底默认配置（极简兜底，防止导入失败）
        注意：真实默认值优先来自 defaults/* 与 legacy unified defaults。
        这里提供硬编码兜底，确保系统能够启动。
        """
        try:
            from .hardcoded_fallbacks import hardcoded_fallback_manager
            return hardcoded_fallback_manager._config
        except Exception as e:
            logger.error(f"加载硬编码兜底配置失败: {e}")
            # 最后的最后兜底 - 确保系统能启动的最小配置
            return {
                "app": {"name": "需求采集系统"},
                "data": {"database": {"path": "backend/data/aidatabase.db"}},
                "llm": {"default_model": "deepseek-chat"}
            }

    def _initialize_sources(self):
        """初始化配置源"""
        logger.info("🔧 开始初始化配置源...")
        try:
            # 定义配置源映射（硬编码，后续可从主配置文件读取）
            source_mappings = {
                # 托底配置（优先作为最低优先级合并，后续源可覆盖）
                "defaults.legacy_unified": "unified_config.defaults.yaml",
                "defaults.base": "defaults/base.yaml",

                # 系统/业务/LLM/数据等模块化配置源
                "system.base": "system/base.yaml",
                "system.performance": "system/performance.yaml",
                "system.security": "system/security.yaml",
                "business.rules": "business/rules.yaml",
                "business.templates": "business/templates.yaml",
                "business.thresholds": "business/thresholds.yaml",
                "llm.models": "llm/models.yaml",
                "llm.scenarios": "llm/scenarios.yaml",
                "llm.prompts": "llm/prompts.yaml",
                "data.database": "data/database.yaml",
                "data.storage": "data/storage.yaml",
                "data.knowledge_base": "data/knowledge_base.yaml",
                "dynamic.keywords": "dynamic/keywords.yaml",
                "dynamic.versions": "dynamic/versions.yaml"
            }

            # 定义优先级（数字越小优先级越高）
            priorities = {
                # 托底配置最先合并，便于后续源覆盖
                "defaults.legacy_unified": -20,
                "defaults.base": -10,

                "dynamic.versions": 1,
                "dynamic.keywords": 2,
                "system.base": 3,
                "business.rules": 4,
                "llm.models": 5,
                "data.database": 6,
                "data.knowledge_base": 7,
                "business.templates": 8,
                "business.thresholds": 9,
                "system.performance": 10,
                "system.security": 11,
                "llm.scenarios": 12,
                "llm.prompts": 13,
                "data.storage": 14
            }

            # 创建配置源
            for source_name, file_path in source_mappings.items():
                full_path = self.config_dir / file_path
                priority = priorities.get(source_name, 999)

                source = ConfigSource(
                    name=source_name,
                    file_path=full_path,
                    priority=priority
                )
                self.config_sources[source_name] = source

            logger.info(f"初始化了 {len(self.config_sources)} 个配置源")

        except Exception as e:
            logger.error(f"配置源初始化失败: {e}")
            raise

    def is_enabled(self) -> bool:
        """检查是否启用模块化加载"""
        return self.enabled

    def enable(self):
        """启用模块化加载"""
        if not self.enabled:
            logger.info("🔧 正在启用模块化配置加载器...")
            self.enabled = True
            self._initialize_sources()
            logger.info("🔧 模块化配置加载器已启用")
        else:
            logger.info("🔧 模块化配置加载器已经启用，强制重新初始化配置源...")
            self._initialize_sources()
            logger.info("🔧 模块化配置加载器重新初始化完成")

    def disable(self):
        """禁用模块化加载"""
        if self.enabled:
            self.enabled = False
            self.config_sources.clear()
            with self.cache_lock:
                self.config_cache.clear()
            logger.info("模块化配置加载器已禁用")

    def get_config(self, config_key: str, default: Any = None) -> Any:
        """获取配置"""
        if not self.enabled:
            return default

        self.monitoring.record_access()

        # 检查缓存
        with self.cache_lock:
            if config_key in self.config_cache:
                self.monitoring.record_access(cache_hit=True)
                return self.config_cache[config_key]

        # 从配置源获取
        try:
            config_data = self._get_from_sources(config_key)
            if config_data is not None:
                with self.cache_lock:
                    self.config_cache[config_key] = config_data
                return config_data
        except Exception as e:
            self.monitoring.record_error()
            logger.error(f"获取配置失败 {config_key}: {e}")

        # 尝试从硬编码兜底获取
        try:
            from .hardcoded_fallbacks import hardcoded_fallback_manager
            fallback_value = hardcoded_fallback_manager.get_config(config_key)
            if fallback_value is not None:
                hardcoded_fallback_manager.log_fallback_usage(config_key, "配置源加载失败")
                # 记录降级日志
                if config_logger and LogConfigSource:
                    config_logger.log_config_fallback(
                        config_key, fallback_value, LogConfigSource.HARDCODED,
                        "配置源加载失败，使用硬编码兜底"
                    )
                return fallback_value
        except Exception as e:
            logger.error(f"硬编码兜底配置获取失败 {config_key}: {e}")

        return default

    def _get_from_sources(self, config_key: str) -> Any:
        """从配置源获取配置"""
        # 如果是完整的配置源键（如 "business.rules"）
        if config_key in self.config_sources:
            source = self.config_sources[config_key]
            return source.config_data

        # 如果是嵌套键（如 "business.rules.retry.max_attempts"）
        parts = config_key.split('.')
        if len(parts) >= 2:
            source_key = f"{parts[0]}.{parts[1]}"
            if source_key in self.config_sources:
                source = self.config_sources[source_key]
                data = source.config_data

                # 遍历嵌套路径
                for part in parts[2:]:
                    if isinstance(data, dict) and part in data:
                        data = data[part]
                    else:
                        return None
                return data

        # 如果上述方法都没找到，遍历所有配置源查找（修复bug）
        for source_name, source in sorted(self.config_sources.items(), key=lambda x: x[1].priority):
            data = source.config_data
            val = self._traverse(data, parts)
            if val is not None:
                return val

        return None

    def reload_config(self, config_key: str = None):
        """重新加载配置"""
        if not self.enabled:
            return

        self.monitoring.record_reload()

        if config_key:
            # 重载特定配置源
            if config_key in self.config_sources:
                self.config_sources[config_key].reload()
                with self.cache_lock:
                    # 清除相关缓存
                    keys_to_remove = [k for k in self.config_cache.keys() if k.startswith(config_key)]
                    for k in keys_to_remove:
                        del self.config_cache[k]
        else:
            # 重载所有配置源
            for source in self.config_sources.values():
                source.reload()
            with self.cache_lock:
                self.config_cache.clear()

    def get_monitoring_stats(self) -> Dict[str, Any]:
        """获取监控统计"""
        stats = self.monitoring.get_stats()
        stats["enabled"] = self.enabled
        stats["sources_count"] = len(self.config_sources)
        stats["cache_size"] = len(self.config_cache)
        return stats

    def create_snapshot(self) -> ConfigSnapshot:
        """创建配置快照"""
        if not self.enabled:
            return ConfigSnapshot(
                version="disabled",
                timestamp=datetime.now(),
                config_data={},
                source_info={}
            )

        # 聚合所有配置
        aggregated_config = {}
        source_info = {}

        # 按优先级排序（数字小的优先级高）
        sorted_sources = sorted(
            self.config_sources.values(),
            key=lambda s: s.priority
        )

        for source in sorted_sources:
            if source.config_data:
                # 深度合并配置
                self._deep_merge(aggregated_config, source.config_data)
                source_info[source.name] = {
                    "priority": source.priority,
                    "last_modified": source.last_modified,
                    "load_time": source.load_time,
                    "error_count": source.error_count
                }

        snapshot = ConfigSnapshot(
            version=f"v{int(time.time())}",
            timestamp=datetime.now(),
            config_data=aggregated_config,
            source_info=source_info
        )

        self.current_snapshot = snapshot
        return snapshot

    def _deep_merge(self, target: Dict, source: Dict):
        """深度合并字典"""
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._deep_merge(target[key], value)
            else:
                target[key] = deepcopy(value)

    # ============== 来源追踪（最小可用） ==============
    def get_with_source(self, config_key: str, default: Any = None) -> Dict[str, Any]:
        """获取配置及来源信息：{"value": ..., "source": ...}
        为不影响现有性能，仅在按需调用时遍历源。
        """
        if not self.enabled:
            return {"value": default, "source": None}
        try:
            value, source = self._get_from_sources_with_source(config_key)
            if value is None:
                return {"value": default, "source": None}
            return {"value": value, "source": source}
        except Exception:
            return {"value": default, "source": None}

    def _get_from_sources_with_source(self, config_key: str) -> (Any, Optional[str]):
        """遍历所有源，返回首个命中值及其来源名。"""
        parts = config_key.split('.') if config_key else []
        # 优先尝试按前两段锁定源
        if len(parts) >= 2:
            source_key = f"{parts[0]}.{parts[1]}"
            if source_key in self.config_sources:
                data = self.config_sources[source_key].config_data
                val = self._traverse(data, parts[2:])
                if val is not None:
                    return val, source_key
        # 退化：遍历所有源，从根开始尝试完整路径
        for source_name, source in sorted(self.config_sources.items(), key=lambda x: x[1].priority):
            data = source.config_data
            val = self._traverse(data, parts)
            if val is not None:
                return val, source_name
        return None, None

    def _traverse(self, data: Any, parts: List[str]) -> Any:
        cur = data
        for p in parts:
            if isinstance(cur, dict) and p in cur:
                cur = cur[p]
            else:
                return None
        return cur


# 全局实例（默认禁用）
modular_config_loader = ModularConfigLoader(enabled=False)


def get_modular_config_loader() -> ModularConfigLoader:
    """获取模块化配置加载器实例"""
    return modular_config_loader
