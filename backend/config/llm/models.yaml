# ============================================================================
# LLM模型配置（从unified_config.yaml拆分）
# ============================================================================
# 文件说明：从unified_config.yaml拆分的LLM模型配置
# 拆分时间：2025-08-18 19:22:20
# 源配置块：llm
# 维护责任：配置管理团队
# 更新频率：根据业务需求
# ============================================================================

default_model: deepseek-chat
default_params:
  temperature: 0.7        # 建议范围 0.1-1.0；越低越保守，越高越发散
  max_tokens: 2000        # 按场景估算输出长度，避免过大
  top_p: 0.9              # nucleus sampling；与temperature联动调参
  frequency_penalty: 0.0  # 0-2；抑制重复
  presence_penalty: 0.0   # 0-2；鼓励新主题
  timeout: 30             # 秒；考虑用户体验与接口超时
  max_retries: 3          # 网络抖动/限流重试次数
models:
  deepseek-chat:
    provider: deepseek
    model_name: deepseek-chat
    api_base: https://api.deepseek.com
    max_tokens: 4000
    temperature: 0.7
    timeout: 45
    top_p: 1
    max_retries: 3
  doubao-1.5-Lite:
    provider: doubao
    model_name: doubao-1.5-Lite
    api_base: https://ark.cn-beijing.volces.com/api/v3
    max_tokens: 4000
    temperature: 0.7
    timeout: 30
    top_p: 1
    max_retries: 3
  qwen-turbo:
    provider: qwen
    model_name: qwen-turbo
    api_base: https://dashscope.aliyuncs.com/api/v1
    max_tokens: 2000
    temperature: 0.7
    timeout: 30
    top_p: 1
    max_retries: 3
  gpt-3.5-turbo:
    provider: openai
    model_name: gpt-3.5-turbo
    api_base: https://api.openai.com/v1
    max_tokens: 4000
    temperature: 0.7
    timeout: 30
    top_p: 1
    max_retries: 3
providers:
  deepseek:
    name: DeepSeek
    base_url: https://api.deepseek.com
    auth_type: bearer_token
    rate_limit: 60
  doubao:
    name: 豆包
    base_url: https://ark.cn-beijing.volces.com/api/v3
    auth_type: bearer_token
    rate_limit: 100
  qwen:
    name: 通义千问
    base_url: https://dashscope.aliyuncs.com/api/v1
    auth_type: bearer_token
    rate_limit: 120
  openai:
    name: OpenAI
    base_url: https://api.openai.com/v1
    auth_type: bearer_token
    rate_limit: 60
model_capabilities:
  deepseek-chat:
    text_generation: true
    conversation: true
    code_generation: true
    reasoning: true
    multilingual: true
    context_length: 32768
  doubao-1.5-Lite:
    text_generation: true
    conversation: true
    code_generation: false
    reasoning: true
    multilingual: true
    context_length: 8192
  qwen-turbo:
    text_generation: true
    conversation: true
    code_generation: true
    reasoning: true
    multilingual: true
    context_length: 8192
  gpt-3.5-turbo:
    text_generation: true
    conversation: true
    code_generation: true
    reasoning: true
    multilingual: true
    context_length: 16384
model_selection:
  strategy: performance_based     # 选择策略：performance_based/cost_based/quality_first
  performance_weights:           # 各维度权重（总和建议=1.0）
    response_time: 0.3
    accuracy: 0.4
    reliability: 0.2
    cost: 0.1
  fallback_enabled: true         # 失败自动降级
  fallback_models:               # 降级顺序（从上到下）
  - deepseek-chat
  - doubao-1.5-Lite
  - qwen-turbo
monitoring:
  performance_tracking: true     # 性能监控：耗时/吞吐
  response_time_tracking: true   # 各模型响应时间
  error_rate_tracking: true      # 错误率趋势
  usage_tracking: true           # 调用次数/并发
  token_usage_tracking: true     # Token用量
  cost_tracking: true            # 费用估算（如可用）
  quality_monitoring: true       # 质量监控开关
  output_quality_scoring: false  # 质量打分（离线/灰度评估）
optimization:
  response_caching: true    # 响应缓存（命中相同输入时降低成本与延迟）
  cache_ttl: 3600           # 缓存TTL秒
  batch_processing: false   # 批量处理（谨慎开启，注意顺序和超时）
  batch_size: 10
  batch_timeout: 5
  model_warmup: true        # 模型预热（启动后发送少量请求以降低首延迟）
  warmup_requests: 3
experimental:
  enable_new_models: false
  test_traffic_ratio: 0.05
  parameter_experiments: false
  experiment_groups: []
_metadata:
  file_version: '1.0'
  split_date: '2025-08-18T19:22:20.095606'
  source_blocks:
  - llm
  config_type: models
  total_items: 167
llm:
  default_model: deepseek-chat
  models:
    deepseek-chat:
      api_base: https://api.deepseek.com
      api_key: ${DEEPSEEK_API_KEY}
      max_retries: 3
      max_tokens: 4000
      model_name: deepseek-chat
      provider: deepseek
      temperature: 0.7
      timeout: 45
      top_p: 1
    doubao-1.5-Lite:
      api_base: https://ark.cn-beijing.volces.com/api/v3
      api_key: ${DOUBAO_API_KEY}
      max_retries: 3
      max_tokens: 8000
      model_name: doubao-1-5-lite-32k-250115
      provider: doubao
      temperature: 0.7
      timeout: 45
      top_p: 1.0
    doubao-pro-32k:
      api_base: https://ark.cn-beijing.volces.com/api/v3
      api_key: ${DOUBAO_API_KEY}
      max_retries: 3
      max_tokens: 8000
      model_name: doubao-pro-32k-241215
      provider: doubao
      temperature: 0.7
      timeout: 45
      top_p: 1.0
    openrouter-gemini-flash:
      api_base: https://openrouter.ai/api/v1
      api_key: ${OPENROUTER_API_KEY}
      max_retries: 3
      max_tokens: 7000
      model_name: google/gemini-2.5-flash
      provider: openrouter
      temperature: 0.7
      timeout: 45
      top_p: 1.0
    qwen-intent:
      api_base: https://dashscope.aliyuncs.com/compatible-mode/v1
      api_key: ${QWEN_API_KEY}
      max_retries: 3
      max_tokens: 4000
      model_name: tongyi-intent-detect-v3
      provider: qwen
      temperature: 0.7
      timeout: 50
      top_p: 1.0
    qwen-max-latest:
      api_base: https://dashscope.aliyuncs.com/compatible-mode/v1
      api_key: ${QWEN_API_KEY}
      max_retries: 3
      max_tokens: 8000
      model_name: qwen-max-latest
      provider: qwen
      temperature: 0.7
      timeout: 50
      top_p: 1.0
    qwen-plus:
      api_base: https://dashscope.aliyuncs.com/compatible-mode/v1
      api_key: ${QWEN_API_KEY}
      max_retries: 3
      max_tokens: 8000
      model_name: qwen-plus
      provider: qwen
      temperature: 0.7
      timeout: 45
      top_p: 1.0
    qwen-turbo-latest:
      api_base: https://dashscope.aliyuncs.com/compatible-mode/v1
      api_key: ${QWEN_API_KEY}
      max_retries: 3
      max_tokens: 8000
      model_name: qwen-turbo-latest
      provider: qwen
      temperature: 0.5
      timeout: 45
      top_p: 1.0
  scenario_mapping:
    apology_generator: doubao-pro-32k
    category_classifier: doubao-1.5-Lite
    clarification_generator: doubao-pro-32k
    conversation_flow: qwen-plus
    document_generator: qwen-plus
    domain_classifier: doubao-1.5-Lite
    domain_guidance_generator: doubao-pro-32k
    empathy_generator: doubao-pro-32k
    greeting_generator: doubao-pro-32k
    information_extractor: doubao-pro-32k
    intent_recognition: doubao-pro-32k
    llm_service: deepseek-chat
    optimized_question_generation: qwen-plus
    structured_intent_classification: doubao-pro-32k
  scenario_params:
    apology_generator:
      max_tokens: 200
      temperature: 0.7
      timeout: 30
    category_classifier:
      max_tokens: 1500
      temperature: 0.3
      timeout: 20
    clarification_generator:
      max_tokens: 5000
      temperature: 0.7
      timeout: 30
    conversation_flow:
      max_tokens: 4000
      temperature: 0.7
      timeout: 30
    default:
      api_base: https://api.deepseek.com
      api_key: ${DEEPSEEK_API_KEY}
      max_retries: 3
      max_tokens: 4000
      model_name: deepseek-chat
      provider: deepseek
      temperature: 0.7
      timeout: 30
    default_generator:
      max_tokens: 200
      temperature: 0.7
      timeout: 30
    document_generator:
      max_tokens: 6000
      temperature: 0.9
      timeout: 60
    domain_classifier:
      max_tokens: 1500
      temperature: 0.3
      timeout: 20
    domain_guidance_generator:
      max_tokens: 300
      temperature: 0.7
      timeout: 30
    empathy_generator:
      max_tokens: 200
      temperature: 0.7
      timeout: 30
    greeting_generator:
      max_tokens: 150
      temperature: 0.7
      timeout: 30
    information_extractor:
      max_tokens: 7000
      temperature: 0.3
      timeout: 45
    intent_recognition:
      max_tokens: 3000
      temperature: 0.3
      timeout: 30
    llm_service:
      max_tokens: 4000
      temperature: 0.7
      timeout: 30
    optimized_question_generation:
      max_tokens: 3500
      temperature: 0.5
      timeout: 12
    structured_intent_classification:
      max_tokens: 4000
      temperature: 0.2
      timeout: 35
