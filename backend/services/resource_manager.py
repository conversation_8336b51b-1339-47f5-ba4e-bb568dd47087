"""
全局资源管理器

负责在应用启动时初始化并持所有无状态的、可共享的组件实例。
这避免了在每次API请求时都重复创建重量级对象，从而显著提升性能。
"""

from backend.config.settings import DATABASE_PATH
from backend.data.db.database_manager import DatabaseManager
from backend.agents.llm_service import AutoGenLLMServiceAgent
from backend.agents.domain_classifier import DomainClassifierAgent
from backend.agents.intent_recognition import IntentRecognitionAgent
from backend.agents.category_classifier import CategoryClassifierAgent
from backend.agents.information_extractor import InformationExtractorAgent
from backend.agents.document_generator import DocumentGenerator
from backend.agents.knowledge_base import KnowledgeBaseAgent
from backend.agents.review_and_refine import AutoGenReviewAndRefineAgent
from backend.agents.message_reply_manager import MessageReplyManager
from backend.agents.dynamic_reply_generator import DynamicReplyGenerator, DynamicReplyFactory
from backend.agents.integrated_reply_system import IntegratedReplySystem
from backend.agents.template_version_manager import TemplateVersionManager


class GlobalResources:
    """
    一个单例容器，用于管理应用生命周期内的所有共享资源。
    在应用启动时被实例化一次。
    """
    def __init__(self):
        # 初始化数据库管理器
        self.db_manager = DatabaseManager(str(DATABASE_PATH))

        # 初始化LLM配置管理器
        from ..config.unified_config_loader import get_unified_config
        self.llm_config_manager = config_service

        # 初始化核心LLM服务Agent (带缓存)
        self.llm_service_agent = AutoGenLLMServiceAgent(
            enable_cache=True,
            cache_size=100,
            cache_ttl=3600
        )

        # 初始化所有无状态的Agent
        self.domain_classifier_agent = DomainClassifierAgent(
            llm_client=self.llm_service_agent,
            agent_name="domain_classifier"
        )

        self.intent_recognition_agent = IntentRecognitionAgent(
            llm_service=self.llm_service_agent,
            agent_name="intent_recognition"
        )

        self.category_classifier_agent = CategoryClassifierAgent(
            llm_client=self.llm_service_agent,
            agent_name="category_classifier"
        )

        self.information_extractor_agent = InformationExtractorAgent(
            llm_service=self.llm_service_agent,
            intent_recognition_agent=self.intent_recognition_agent
        )

        self.document_generator_agent = DocumentGenerator(
            llm_client=self.llm_service_agent,
            db_manager=self.db_manager
        )

        self.knowledge_base_agent = KnowledgeBaseAgent(db_path=str(DATABASE_PATH))

        self.review_and_refine_agent = AutoGenReviewAndRefineAgent(
            llm_client=self.llm_service_agent,
            llm_config={"db_path": str(DATABASE_PATH)}
        )

        # 初始化回复系统组件
        self.reply_manager = MessageReplyManager(llm_client=self.llm_service_agent)
        dynamic_reply_generator = DynamicReplyGenerator(llm_client=self.llm_service_agent)
        self.reply_factory = DynamicReplyFactory(dynamic_reply_generator)
        self.integrated_reply_system = IntegratedReplySystem(llm_client=self.llm_service_agent, reply_manager=self.reply_manager)
        self.version_manager = TemplateVersionManager()

        # 动态地将知识库实例设置给需要它的Agent
        self.domain_classifier_agent.knowledge_base = self.knowledge_base_agent
        self.information_extractor_agent.knowledge_base_agent = self.knowledge_base_agent

    def create_conversation_flow_agent(self, session_id: str, user_id: str):
        """
        为特定用户会话创建ConversationFlowAgent实例

        Args:
            session_id: 会话ID
            user_id: 用户ID

        Returns:
            配置好的ConversationFlowAgent实例
        """
        from backend.agents.factory import agent_factory

        # 为每个用户会话创建独立的Agent实例
        return agent_factory.get_conversation_flow_agent(
            session_id=session_id,
            # 传递额外的依赖覆盖
            information_extractor_agent=self.information_extractor_agent,
            review_and_refine_agent=self.review_and_refine_agent,
            domain_classifier_agent=self.domain_classifier_agent,
            category_classifier_agent=self.category_classifier_agent,
            reply_manager=self.reply_manager,
            reply_factory=self.reply_factory,
            integrated_reply_system=self.integrated_reply_system,
            version_manager=self.version_manager
        )
