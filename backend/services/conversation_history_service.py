# -*- coding: utf-8 -*-
"""
对话历史统一管理服务

提供统一的对话历史获取、格式化和管理功能，
避免在各个模块中重复实现相同的逻辑。
"""

import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

from ..data.db.message_manager import MessageManager
from ..config.unified_config_loader import get_unified_config


class HistoryFormat(Enum):
    """历史记录格式类型"""
    SIMPLE = "simple"  # 简单格式：user: xxx\nassistant: xxx
    DETAILED = "detailed"  # 详细格式：包含时间戳等
    TEMPLATE = "template"  # 模板格式：用于LLM模板


@dataclass
class HistoryConfig:
    """历史记录配置"""
    max_turns: int = 15  # 默认最大对话轮数
    max_message_length: int = 200  # 默认单条消息最大长度
    format_type: HistoryFormat = HistoryFormat.SIMPLE

    @classmethod
    def from_unified_config(cls):
        """从统一配置创建实例"""
        
        return cls(
            max_turns=config.get_threshold("limits.max_history_items", 15),
            max_message_length=config.get_threshold("quality.max_word_count", 200),
            format_type=HistoryFormat.SIMPLE
        )
    include_system_messages: bool = False  # 是否包含系统消息


class ConversationHistoryService:
    """对话历史统一管理服务"""

    def __init__(self, message_manager: MessageManager):
        """
        初始化对话历史服务

        Args:
            message_manager: 消息管理器实例
        """
        self.message_manager = message_manager
        self.logger = logging.getLogger(__name__)

        # 缓存配置对象
        self.

        # 从配置中获取默认设置
        self.default_config = HistoryConfig(
            max_turns=self.config.get_threshold("system.performance.max_conversation_turns", 15),
            max_message_length=self.config.get_threshold("system.performance.max_message_length", 200),
            format_type=HistoryFormat.SIMPLE
        )

    async def get_conversation_history(
        self,
        session_id: str,
        user_id: str,
        config: Optional[HistoryConfig] = None
    ) -> str:
        """
        获取格式化的对话历史

        Args:
            session_id: 会话ID
            user_id: 用户ID
            config: 历史记录配置，如果为None则使用默认配置

        Returns:
            str: 格式化的对话历史字符串
        """
        if not config:
            config = self.default_config

        try:
            # 获取原始历史记录
            raw_history = await self._get_raw_history(session_id, user_id, config.max_turns)

            if not raw_history:
                return self._get_empty_history_message()

            # 格式化历史记录
            formatted_history = self._format_history(raw_history, config)

            return formatted_history

        except Exception as e:
            self.logger.warning(f"获取对话历史失败 (session_id: {session_id}): {str(e)}")
            return self._get_empty_history_message()

    async def get_recent_context(
        self,
        session_id: str,
        user_id: str,
        turns: int = None
    ) -> List[Dict[str, Any]]:
        """
        获取最近几轮对话的原始数据

        Args:
            session_id: 会话ID
            user_id: 用户ID
            turns: 对话轮数

        Returns:
            List[Dict[str, Any]]: 原始对话数据列表
        """
        try:
            if turns is None:
                turns = self.config.get_threshold("performance.retry.default", 5)
            return await self._get_raw_history(session_id, user_id, turns)
        except Exception as e:
            self.logger.warning(f"获取最近对话上下文失败: {str(e)}")
            return []

    async def get_recent_messages(
        self,
        session_id: str,
        user_id: str,
        limit: int = None
    ) -> List[Dict[str, Any]]:
        """
        获取最近的消息（兼容性方法）

        Args:
            session_id: 会话ID
            user_id: 用户ID
            limit: 消息数量限制

        Returns:
            List[Dict[str, Any]]: 最近的消息列表
        """
        try:
            if limit is None:
                limit = self.config.get_threshold("limits.default_max_items", 10)
            # 将limit转换为轮数（每轮包含用户和助手两条消息）
            turns = max(1, limit // 2)
            return await self._get_raw_history(session_id, user_id, turns)
        except Exception as e:
            self.logger.warning(f"获取最近消息失败 (session_id: {session_id}): {str(e)}")
            return []

    async def _get_raw_history(
        self,
        session_id: str,
        user_id: str,
        max_turns: int
    ) -> List[Dict[str, Any]]:
        """
        获取原始历史记录

        Args:
            session_id: 会话ID
            user_id: 用户ID
            max_turns: 最大轮数

        Returns:
            List[Dict[str, Any]]: 原始历史记录列表
        """
        if not self.message_manager or not session_id:
            return []

        # 计算需要获取的消息数量（每轮对话包含用户和助手两条消息）
        limit = max_turns * 2

        history = await self.message_manager.load_conversation_history(session_id, user_id, limit=limit)
        return history or []

    def _format_history(
        self,
        raw_history: List[Dict[str, Any]],
        config: HistoryConfig
    ) -> str:
        """
        格式化历史记录

        Args:
            raw_history: 原始历史记录
            config: 格式化配置

        Returns:
            str: 格式化后的历史记录字符串
        """
        if not raw_history:
            return self._get_empty_history_message()

        formatted_messages = []

        for msg in raw_history:
            # 过滤系统消息（如果配置不包含）
            if not config.include_system_messages and msg.get("message_type") == "system":
                continue

            role = msg.get("role", "user")
            content = msg.get("content", "").strip()

            if not content:
                continue

            # 限制消息长度
            if len(content) > config.max_message_length:
                content = content[:config.max_message_length] + "..."

            # 根据格式类型进行格式化
            formatted_msg = self._format_single_message(role, content, config.format_type, msg)
            if formatted_msg:
                formatted_messages.append(formatted_msg)

        return "\n".join(formatted_messages) if formatted_messages else self._get_empty_history_message()

    def _format_single_message(
        self,
        role: str,
        content: str,
        format_type: HistoryFormat,
        raw_msg: Dict[str, Any]
    ) -> str:
        """
        格式化单条消息

        Args:
            role: 角色（user/assistant）
            content: 消息内容
            format_type: 格式类型
            raw_msg: 原始消息数据

        Returns:
            str: 格式化后的消息
        """
        if format_type == HistoryFormat.SIMPLE:
            return f"{role}: {content}"

        elif format_type == HistoryFormat.TEMPLATE:
            # 使用配置的模板格式
            if role == "user":
                prefix = self.config.get_message_template('formatting.history.user_prefix', default="用户: ")
            else:
                prefix = self.config.get_message_template('formatting.history.ai_prefix', default="AI: ")
            return f"{prefix}{content}"

        elif format_type == HistoryFormat.DETAILED:
            timestamp = raw_msg.get("timestamp", "")
            return f"[{timestamp}] {role}: {content}"

        else:
            return f"{role}: {content}"

    def _get_empty_history_message(self) -> str:
        """获取空历史记录的默认消息"""
        return self.config.get_message_template(
            'formatting.history.empty',
            default="暂无历史信息（这是新的对话）"
        )

    def update_default_config(self, **kwargs):
        """
        更新默认配置

        Args:
            **kwargs: 配置参数
        """
        for key, value in kwargs.items():
            if hasattr(self.default_config, key):
                setattr(self.default_config, key, value)
                self.logger.info(f"更新对话历史默认配置: {key} = {value}")


# 全局历史服务实例（单例模式）
_history_service_instance = None


def get_history_service(message_manager: MessageManager = None) -> ConversationHistoryService:
    """
    获取对话历史服务实例（单例模式）

    Args:
        message_manager: 消息管理器实例（仅在首次调用时需要）

    Returns:
        ConversationHistoryService: 历史服务实例
    """
    global _history_service_instance

    if _history_service_instance is None:
        if message_manager is None:
            raise ValueError("首次调用时必须提供 message_manager 参数")
        _history_service_instance = ConversationHistoryService(message_manager)

    return _history_service_instance
