#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内容审查模块 - Content Moderation

此模块提供用户输入内容的安全审查功能，包括：
1. 关键词检测：基于配置的违规词汇检测
2. 策略决策：根据违规类型决定处理动作
3. 内容处理：掩码、警告、阻止等处理方式
4. 审计日志：记录安全事件用于监控和分析

使用方式：
```python
from backend.safety.content_moderation import ContentModerator

moderator = ContentModerator()
result = moderator.check("用户输入文本", user_id="user123", session_id="session456")

if result.action == "BLOCK":
    return error_response(result.reason)
elif result.action == "WARN":
    log_security_event(result)
    # 继续处理，使用 result.processed_text
```

设计原则：
- 最小侵入：与现有系统无缝集成
- 可配置：通过 unified_config.yaml 灵活配置策略
- 可扩展：支持添加新的检测规则和处理动作
- 高性能：利用关键词系统的缓存机制
"""

import logging
import re
import time
from typing import Dict, List, Optional, Set, Tuple, Any
from dataclasses import dataclass
from enum import Enum

from backend.config import config_service
from backend.config.keywords_loader import get_keywords_loader


class ModerationAction(Enum):
    """内容审查动作枚举"""
    ALLOW = "ALLOW"         # 允许：正常处理
    WARN = "WARN"           # 警告：记录日志并继续处理
    MASK = "MASK"           # 掩码：脱敏处理后继续
    BLOCK = "BLOCK"         # 阻止：拒绝处理并返回提示
    ESCALATE = "ESCALATE"   # 升级：标记为高风险事件


@dataclass
class ContentModerationResult:
    """内容审查结果"""
    is_allowed: bool                    # 是否允许继续处理
    action: ModerationAction           # 处理动作
    category: Optional[str] = None     # 违规类别
    matched_terms: List[str] = None    # 匹配的违规词汇
    processed_text: Optional[str] = None  # 处理后的文本（掩码等）
    reason: Optional[str] = None       # 处理原因
    confidence: float = 1.0            # 检测置信度
    metadata: Dict[str, Any] = None    # 额外元数据

    def __post_init__(self):
        if self.matched_terms is None:
            self.matched_terms = []
        if self.metadata is None:
            self.metadata = {}


class ContentModerator:
    """
    内容审查器

    功能：
    - 基于关键词的违规内容检测
    - 可配置的处理策略
    - 文本掩码和净化
    - 安全事件记录
    """

    def __init__(self):
        """初始化内容审查器"""
        self.logger = logging.getLogger(__name__)
        self.
        self.keywords_loader = get_keywords_loader()

        # 加载配置
        self._load_moderation_config()

        # 统计信息
        self.stats = {
            "total_checks": 0,
            "violations_detected": 0,
            "actions_taken": {action.value: 0 for action in ModerationAction}
        }

        self.logger.info("内容审查器初始化完成")

    def _load_moderation_config(self):
        """加载审查配置"""
        try:
            # 基础配置
            self.enabled = self.config.get_config_value("security.content_moderation.enabled", True)
            self.default_action = ModerationAction(
                self.config.get_config_value("security.content_moderation.default_action", "WARN")
            )

            # 动作映射
            actions_config = self.config.get_config_value("security.content_moderation.actions", {})
            self.category_actions = {}
            for category, action_str in actions_config.items():
                try:
                    self.category_actions[category] = ModerationAction(action_str)
                except ValueError:
                    self.logger.warning(f"无效的动作配置: {category}={action_str}, 使用默认动作")
                    self.category_actions[category] = self.default_action

            # 掩码配置
            masking_config = self.config.get_config_value("security.content_moderation.masking", {})
            self.mask_replacement = masking_config.get("replacement", "*")
            self.keep_first_char = masking_config.get("keep_first_char", True)
            self.min_mask_length = masking_config.get("min_mask_length", 2)

            # 日志配置
            logging_config = self.config.get_config_value("security.content_moderation.logging", {})
            self.log_violations = logging_config.get("log_violations", True)
            self.include_original_text = logging_config.get("include_original_text", False)

            self.logger.info(f"内容审查配置加载完成，启用状态: {self.enabled}")

        except Exception as e:
            self.logger.error(f"加载审查配置失败: {e}")
            # 使用安全的默认配置
            self.enabled = True
            self.default_action = ModerationAction.WARN
            self.category_actions = {}

    def check(self, text: str, user_id: str = None, session_id: str = None) -> ContentModerationResult:
        """
        检查文本内容

        Args:
            text: 待检查的文本
            user_id: 用户ID
            session_id: 会话ID

        Returns:
            ContentModerationResult: 审查结果
        """
        self.stats["total_checks"] += 1

        # 如果未启用，直接通过
        if not self.enabled:
            return ContentModerationResult(
                is_allowed=True,
                action=ModerationAction.ALLOW,
                processed_text=text
            )

        try:
            # 执行检测
            violations = self._detect_violations(text)

            if not violations:
                # 无违规，允许通过
                return ContentModerationResult(
                    is_allowed=True,
                    action=ModerationAction.ALLOW,
                    processed_text=text
                )

            # 有违规，确定处理策略
            primary_violation = self._get_primary_violation(violations)
            action = self._determine_action(primary_violation["category"])

            # 处理文本
            processed_text = self._process_text(text, violations, action)

            # 构建结果
            result = ContentModerationResult(
                is_allowed=(action != ModerationAction.BLOCK),
                action=action,
                category=primary_violation["category"],
                matched_terms=primary_violation["terms"],
                processed_text=processed_text,
                reason=self._get_reason_message(action, primary_violation["category"]),
                confidence=primary_violation.get("confidence", 1.0),
                metadata={
                    "user_id": user_id,
                    "session_id": session_id,
                    "all_violations": violations,
                    "timestamp": time.time()
                }
            )

            # 更新统计
            self.stats["violations_detected"] += 1
            self.stats["actions_taken"][action.value] += 1

            # 记录安全事件
            if self.log_violations:
                self._log_security_event(result, text)

            return result

        except Exception as e:
            self.logger.error(f"内容审查过程中发生错误: {e}")
            # 出错时采用保守策略
            return ContentModerationResult(
                is_allowed=True,
                action=ModerationAction.ALLOW,
                processed_text=text,
                reason="审查系统暂时不可用"
            )

    def _detect_violations(self, text: str) -> List[Dict[str, Any]]:
        """检测违规内容"""
        violations = []

        try:
            # 获取安全关键词
            safety_keywords = self.keywords_loader.get_safety_keywords()

            # 预处理文本
            text_lower = text.lower()

            # 逐类别检测
            for category, keywords in safety_keywords.items():
                matched_terms = []

                for keyword in keywords:
                    if keyword.lower() in text_lower:
                        matched_terms.append(keyword)

                if matched_terms:
                    violations.append({
                        "category": category,
                        "terms": matched_terms,
                        "confidence": 1.0  # 关键词匹配为高置信度
                    })

        except Exception as e:
            self.logger.error(f"违规检测失败: {e}")

        return violations

    def _get_primary_violation(self, violations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """获取主要违规（用于决定处理策略）"""
        if not violations:
            return {}

        # 按严重程度排序（可以根据需要调整优先级）
        severity_order = [
            "sexual_content", "hate_speech", "violence",
            "self_harm", "profanity", "jailbreak", "pii_patterns"
        ]

        for category in severity_order:
            for violation in violations:
                if violation["category"] == category:
                    return violation

        # 如果没有匹配到预定义顺序，返回第一个
        return violations[0]

    def _determine_action(self, category: str) -> ModerationAction:
        """确定处理动作"""
        return self.category_actions.get(category, self.default_action)

    def _process_text(self, text: str, violations: List[Dict[str, Any]], action: ModerationAction) -> str:
        """处理文本内容"""
        if action == ModerationAction.MASK:
            return self._mask_text(text, violations)
        elif action in [ModerationAction.WARN, ModerationAction.ALLOW]:
            # 对于警告和允许，可以选择性地进行轻度净化
            return self._light_sanitize(text, violations)
        else:
            # BLOCK 和 ESCALATE 不需要处理原文
            return text

    def _mask_text(self, text: str, violations: List[Dict[str, Any]]) -> str:
        """掩码处理文本"""
        masked_text = text

        for violation in violations:
            for term in violation["terms"]:
                if self.keep_first_char and len(term) > 1:
                    replacement = term[0] + self.mask_replacement * max(len(term) - 1, self.min_mask_length - 1)
                else:
                    replacement = self.mask_replacement * max(len(term), self.min_mask_length)

                # 使用正则表达式进行大小写不敏感的替换
                pattern = re.escape(term)
                masked_text = re.sub(pattern, replacement, masked_text, flags=re.IGNORECASE)

        return masked_text

    def _light_sanitize(self, text: str, violations: List[Dict[str, Any]]) -> str:
        """轻度净化（仅对明显的脏话进行替换）"""
        # 目前只对 profanity 类别进行轻度净化
        sanitized_text = text

        for violation in violations:
            if violation["category"] == "profanity":
                for term in violation["terms"]:
                    if len(term) > 1:
                        replacement = term[0] + "*" * (len(term) - 1)
                        pattern = re.escape(term)
                        sanitized_text = re.sub(pattern, replacement, sanitized_text, flags=re.IGNORECASE)

        return sanitized_text

    def _get_reason_message(self, action: ModerationAction, category: str) -> str:
        """获取处理原因消息"""
        try:
            if action == ModerationAction.BLOCK:
                template_key = f"error.safety.blocked_{category}"
                return self.config.get_message_template(template_key) or "内容不符合社区规范，请重新输入。"
            elif action == ModerationAction.WARN:
                # 自残类优先使用关怀模板
                if category == "self_harm":
                    care_key = "error.safety.self_harm_support"
                    care_msg = self.config.get_message_template(care_key)
                    if care_msg:
                        return care_msg
                template_key = f"error.safety.warning_{category}"
                return self.config.get_message_template(template_key) or "检测到不当内容，已做适当处理。"
            else:
                return ""
        except Exception:
            return "内容审查提示"

    def _log_security_event(self, result: ContentModerationResult, original_text: str):
        """记录安全事件"""
        try:
            event_data = {
                "event_type": "content_moderation",
                "action": result.action.value,
                "category": result.category,
                "matched_terms_count": len(result.matched_terms),
                "user_id": result.metadata.get("user_id"),
                "session_id": result.metadata.get("session_id"),
                "timestamp": result.metadata.get("timestamp"),
                "confidence": result.confidence
            }

            # 根据配置决定是否包含原始文本
            if self.include_original_text:
                event_data["original_text"] = original_text
                event_data["processed_text"] = result.processed_text
            else:
                event_data["text_length"] = len(original_text)
                event_data["matched_terms"] = result.matched_terms  # 仍然记录匹配的词汇用于分析

            self.logger.info(f"安全事件: {event_data}")

        except Exception as e:
            self.logger.error(f"记录安全事件失败: {e}")

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            "enabled": self.enabled,
            "categories_configured": len(self.category_actions)
        }

    def reload_config(self):
        """重新加载配置"""
        try:
            self._load_moderation_config()
            self.logger.info("内容审查配置重新加载完成")
        except Exception as e:
            self.logger.error(f"重新加载配置失败: {e}")


# 全局实例（单例模式）
_content_moderator = None

def get_content_moderator() -> ContentModerator:
    """获取内容审查器实例"""
    global _content_moderator
    if _content_moderator is None:
        _content_moderator = ContentModerator()
    return _content_moderator


# 导出主要接口
__all__ = [
    'ContentModerator',
    'ContentModerationResult',
    'ModerationAction',
    'get_content_moderator'
]
