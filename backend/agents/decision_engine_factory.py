#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一决策引擎工厂

提供统一的决策引擎创建和管理接口，解决多种决策引擎实现不一致的问题。
支持不同的决策引擎实现，但提供统一的访问接口。

设计原则：
1. 单一入口：所有决策引擎通过工厂创建
2. 接口统一：不同实现提供相同的接口
3. 配置驱动：通过配置选择具体实现
4. 向后兼容：保持与现有代码的兼容性
"""

import logging
from typing import Optional, Dict, Any
from enum import Enum

from backend.config import config_service


class DecisionEngineType(Enum):
    """决策引擎类型枚举"""
    SIMPLIFIED = "simplified"          # 简化决策引擎（当前主要使用）
    UNIFIED = "unified"                # 统一决策引擎（新架构）
    ADAPTER = "adapter"                # 适配器模式（兼容性）


class DecisionEngineFactory:
    """统一决策引擎工厂"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.
        self._instances = {}  # 缓存实例

    def create_decision_engine(self,
                             engine_type: Optional[DecisionEngineType] = None,
                                **kwargs) -> Any:
        """
        创建决策引擎实例

        Args:
            engine_type: 决策引擎类型，如果为None则从配置读取
            **kwargs: 传递给决策引擎的参数

        Returns:
            决策引擎实例
        """
        # 确定引擎类型
        if engine_type is None:
            engine_type = self._get_default_engine_type()

        # 检查缓存
        cache_key = f"{engine_type.value}_{hash(frozenset(kwargs.items()))}"
        if cache_key in self._instances:
            self.logger.debug(f"返回缓存的决策引擎实例: {engine_type.value}")
            return self._instances[cache_key]

        # 创建新实例
        try:
            if engine_type == DecisionEngineType.SIMPLIFIED:
                instance = self._create_simplified_engine(**kwargs)
            elif engine_type == DecisionEngineType.UNIFIED:
                instance = self._create_unified_engine(**kwargs)
            elif engine_type == DecisionEngineType.ADAPTER:
                instance = self._create_adapter_engine(**kwargs)
            else:
                raise ValueError(f"不支持的决策引擎类型: {engine_type}")

            # 缓存实例
            self._instances[cache_key] = instance
            self.logger.info(f"创建决策引擎实例: {engine_type.value}")

            return instance

        except Exception as e:
            self.logger.error(f"创建决策引擎失败: {engine_type.value}, 错误: {e}")
            # 回退到简化引擎
            if engine_type != DecisionEngineType.SIMPLIFIED:
                self.logger.warning("回退到简化决策引擎")
                return self._create_simplified_engine(**kwargs)
            raise

    def _get_default_engine_type(self) -> DecisionEngineType:
        """获取默认的决策引擎类型"""
        try:
            engine_config = self.config.get_config_value("system.decision_engine.type", "simplified")
            return DecisionEngineType(engine_config)
        except (ValueError, KeyError):
            self.logger.warning("配置中的决策引擎类型无效，使用默认值")
            return DecisionEngineType.SIMPLIFIED

    def _create_simplified_engine(self, **kwargs) -> Any:
        """创建简化决策引擎"""
        from .simplified_decision_engine import SimplifiedDecisionEngine
        return SimplifiedDecisionEngine(**kwargs)

    def _create_unified_engine(self, **kwargs) -> Any:
        """创建统一决策引擎"""
        from .unified_decision_engine import UnifiedDecisionEngine
        return UnifiedDecisionEngine(**kwargs)

    def _create_adapter_engine(self, **kwargs) -> Any:
        """创建适配器决策引擎"""
        from .decision_engine_adapter import DecisionEngineAdapter
        use_unified = kwargs.pop('use_unified_engine', True)
        return DecisionEngineAdapter(use_unified_engine=use_unified, **kwargs)

    def get_recommended_engine(self) -> Any:
        """
        获取推荐的决策引擎实例

        根据当前系统状态和配置，返回最适合的决策引擎实例
        """
        # 当前推荐使用简化决策引擎
        return self.create_decision_engine(DecisionEngineType.SIMPLIFIED)

    def clear_cache(self):
        """清理实例缓存"""
        self._instances.clear()
        self.logger.info("决策引擎实例缓存已清理")

    def get_engine_info(self) -> Dict[str, Any]:
        """获取决策引擎信息"""
        return {
            "default_type": self._get_default_engine_type().value,
            "available_types": [t.value for t in DecisionEngineType],
            "cached_instances": len(self._instances),
            "recommended": "simplified"
        }


# 全局工厂实例
_global_factory: Optional[DecisionEngineFactory] = None


def get_decision_engine_factory() -> DecisionEngineFactory:
    """获取全局决策引擎工厂实例"""
    global _global_factory
    if _global_factory is None:
        _global_factory = DecisionEngineFactory()
    return _global_factory


def create_decision_engine(engine_type: Optional[DecisionEngineType] = None, **kwargs) -> Any:
    """
    便捷函数：创建决策引擎实例

    Args:
        engine_type: 决策引擎类型
        **kwargs: 传递给决策引擎的参数

    Returns:
        决策引擎实例
    """
    factory = get_decision_engine_factory()
    return factory.create_decision_engine(engine_type, **kwargs)


def get_recommended_decision_engine(**kwargs) -> Any:
    """
    便捷函数：获取推荐的决策引擎实例

    Args:
        **kwargs: 传递给决策引擎的参数

    Returns:
        推荐的决策引擎实例
    """
    factory = get_decision_engine_factory()
    return factory.get_recommended_engine()


# 兼容性函数 - 保持与现有代码的兼容性
def get_simplified_decision_engine(**kwargs):
    """兼容性函数：获取简化决策引擎实例"""
    return create_decision_engine(DecisionEngineType.SIMPLIFIED, **kwargs)


def get_unified_decision_engine(**kwargs):
    """兼容性函数：获取统一决策引擎实例"""
    return create_decision_engine(DecisionEngineType.UNIFIED, **kwargs)


# 导出接口
__all__ = [
    'DecisionEngineType',
    'DecisionEngineFactory',
    'get_decision_engine_factory',
    'create_decision_engine',
    'get_recommended_decision_engine',
    'get_simplified_decision_engine',
    'get_unified_decision_engine'
]
