"""
Agent实例池管理器
实现会话级Agent缓存和组件实例复用，减少重复初始化

核心功能：
1. 会话级Agent实例缓存
2. 组件实例复用机制
3. 内存管理和生命周期控制
4. 线程安全的并发访问
5. 配置驱动的缓存策略

设计原则：
- 遵循开发规范，使用统一配置服务
- 避免硬编码，所有参数可配置
- 线程安全，支持多用户并发
- 内存可控，防止内存泄漏
- 监控友好，提供详细统计信息
"""

import threading
import time
from typing import Dict, Any
from dataclasses import dataclass
from backend.config import config_service
from backend.config.optimization_config import get_optimization_manager
import logging


@dataclass
class AgentCacheConfig:
    """Agent缓存配置"""
    # 缓存策略
    enable_session_cache: bool = True
    enable_component_cache: bool = True

    # 生命周期管理
    session_timeout_minutes: int = 30
    max_cached_sessions: int = 100
    cleanup_interval_minutes: int = 5

    # 内存控制
    max_memory_mb: int = 500
    memory_check_interval: int = 10

    # 监控配置
    enable_metrics: bool = True
    metrics_report_interval: int = 100


@dataclass
class CachedAgentInfo:
    """缓存的Agent信息"""
    agent: Any
    session_id: str
    user_id: str
    created_at: float
    last_accessed: float
    access_count: int = 0

    def update_access(self):
        """更新访问信息"""
        self.last_accessed = time.time()
        self.access_count += 1

    def is_expired(self, timeout_seconds: int) -> bool:
        """检查是否过期"""
        return time.time() - self.last_accessed > timeout_seconds


@dataclass
class PoolMetrics:
    """实例池统计信息"""
    # 缓存统计
    cache_hits: int = 0
    cache_misses: int = 0
    cache_evictions: int = 0

    # 实例统计
    total_agents_created: int = 0
    total_agents_reused: int = 0
    current_cached_count: int = 0

    # 性能统计
    avg_creation_time: float = 0.0
    avg_reuse_time: float = 0.0
    total_memory_saved_mb: float = 0.0

    # 并发统计
    concurrent_requests: int = 0
    max_concurrent_requests: int = 0

    def get_cache_hit_rate(self) -> float:
        """获取缓存命中率"""
        total = self.cache_hits + self.cache_misses
        return self.cache_hits / total if total > 0 else 0.0

    def get_reuse_rate(self) -> float:
        """获取实例复用率"""
        total = self.total_agents_created + self.total_agents_reused
        return self.total_agents_reused / total if total > 0 else 0.0


class AgentInstancePool:
    """Agent实例池管理器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # 加载配置
        self.config = self._load_config()

        # 缓存存储
        self._session_cache: Dict[str, CachedAgentInfo] = {}
        self._component_cache: Dict[str, Any] = {}

        # 线程安全
        self._cache_lock = threading.RLock()
        self._cleanup_lock = threading.Lock()

        # 统计信息
        self.metrics = PoolMetrics()

        # 后台清理任务
        self._cleanup_thread = None
        self._shutdown_event = threading.Event()

        # 跟踪初始化
        from backend.config.unified_config_loader import _init_tracker
        _init_tracker.track_init("AgentInstancePool")

        self._start_background_tasks()
        self.logger.info("Agent实例池初始化完成")

    def _load_config(self) -> AgentCacheConfig:
        """加载缓存配置"""
        try:
            unified_
            optimization_manager = get_optimization_manager()

            # 从统一配置获取缓存参数
            cache_config = unified_config.get_config_value("agent_cache", {})

            return AgentCacheConfig(
                enable_session_cache=cache_config.get("enable_session_cache", True),
                enable_component_cache=cache_config.get("enable_component_cache", True),
                session_timeout_minutes=cache_config.get("session_timeout_minutes", 30),
                max_cached_sessions=cache_config.get("max_cached_sessions", 100),
                cleanup_interval_minutes=cache_config.get("cleanup_interval_minutes", 5),
                max_memory_mb=cache_config.get("max_memory_mb", 500),
                memory_check_interval=cache_config.get("memory_check_interval", 10),
                enable_metrics=optimization_manager.is_init_tracking_enabled(),
                metrics_report_interval=cache_config.get("metrics_report_interval", 100)
            )
        except Exception as e:
            self.logger.warning(f"加载缓存配置失败，使用默认配置: {e}")
            return AgentCacheConfig()

    def get_or_create_agent(self, session_id: str, user_id: str,
                            agent_factory, **kwargs) -> Any:
        """
        获取或创建Agent实例

        Args:
            session_id: 会话ID
            user_id: 用户ID
            agent_factory: Agent工厂实例
            **kwargs: 额外参数

        Returns:
            Agent实例
        """
        if not self.config.enable_session_cache:
            # 缓存禁用，直接创建
            return self._create_new_agent(session_id, agent_factory, **kwargs)

        cache_key = f"{user_id}:{session_id}"

        with self._cache_lock:
            # 检查缓存
            if cache_key in self._session_cache:
                cached_info = self._session_cache[cache_key]

                # 检查是否过期
                timeout_seconds = self.config.session_timeout_minutes * 60
                if not cached_info.is_expired(timeout_seconds):
                    # 缓存命中
                    cached_info.update_access()
                    self.metrics.cache_hits += 1
                    self.metrics.total_agents_reused += 1

                    if self.config.enable_metrics:
                        self.logger.debug(f"🎯 Agent缓存命中: {cache_key} (访问次数: {cached_info.access_count})")

                    return cached_info.agent
                else:
                    # 缓存过期，清理
                    del self._session_cache[cache_key]
                    self.metrics.cache_evictions += 1
                    self.logger.debug(f"🗑️ 清理过期Agent缓存: {cache_key}")

            # 缓存未命中，创建新实例
            self.metrics.cache_misses += 1
            start_time = time.time()

            agent = self._create_new_agent(session_id, agent_factory, **kwargs)
            creation_time = time.time() - start_time

            # 更新统计
            self.metrics.total_agents_created += 1
            self.metrics.avg_creation_time = (
                (self.metrics.avg_creation_time * (self.metrics.total_agents_created - 1) + creation_time)
                / self.metrics.total_agents_created
            )

            # 缓存新实例
            cached_info = CachedAgentInfo(
                agent=agent,
                session_id=session_id,
                user_id=user_id,
                created_at=time.time(),
                last_accessed=time.time(),
                access_count=1
            )

            # 检查缓存容量
            if len(self._session_cache) >= self.config.max_cached_sessions:
                self._evict_oldest_cache()

            self._session_cache[cache_key] = cached_info
            self.metrics.current_cached_count = len(self._session_cache)

            if self.config.enable_metrics:
                self.logger.info(f"🆕 创建并缓存新Agent: {cache_key} (耗时: {creation_time:.3f}s)")

            return agent

    def _create_new_agent(self, session_id: str, agent_factory, **kwargs) -> Any:
        """创建新的Agent实例"""
        return agent_factory._create_conversation_flow_agent_direct(session_id, **kwargs)

    def _evict_oldest_cache(self):
        """清理最旧的缓存项"""
        if not self._session_cache:
            return

        # 找到最旧的缓存项
        oldest_key = min(self._session_cache.keys(),
                         key=lambda k: self._session_cache[k].last_accessed)

        del self._session_cache[oldest_key]
        self.metrics.cache_evictions += 1
        self.logger.debug(f"🗑️ 清理最旧Agent缓存: {oldest_key}")

    def _start_background_tasks(self):
        """启动后台清理任务"""
        if self.config.cleanup_interval_minutes > 0:
            self._cleanup_thread = threading.Thread(
                target=self._background_cleanup,
                daemon=True,
                name="AgentPoolCleanup"
            )
            self._cleanup_thread.start()

    def _background_cleanup(self):
        """后台清理任务"""
        cleanup_interval = self.config.cleanup_interval_minutes * 60

        while not self._shutdown_event.wait(cleanup_interval):
            try:
                self._cleanup_expired_cache()
                self._report_metrics()
            except Exception as e:
                self.logger.error(f"后台清理任务异常: {e}")

    def _cleanup_expired_cache(self):
        """清理过期缓存"""
        with self._cleanup_lock:
            timeout_seconds = self.config.session_timeout_minutes * 60
            expired_keys = []

            with self._cache_lock:
                for key, cached_info in self._session_cache.items():
                    if cached_info.is_expired(timeout_seconds):
                        expired_keys.append(key)

                for key in expired_keys:
                    del self._session_cache[key]
                    self.metrics.cache_evictions += 1

                self.metrics.current_cached_count = len(self._session_cache)

            if expired_keys and self.config.enable_metrics:
                self.logger.info(f"🧹 后台清理过期缓存: {len(expired_keys)} 个")

    def _report_metrics(self):
        """报告统计信息"""
        if not self.config.enable_metrics:
            return

        total_requests = self.metrics.cache_hits + self.metrics.cache_misses
        if total_requests > 0 and total_requests % self.config.metrics_report_interval == 0:
            self.logger.info(
                f"📊 Agent池统计 - "
                f"缓存命中率: {self.metrics.get_cache_hit_rate():.2%}, "
                f"实例复用率: {self.metrics.get_reuse_rate():.2%}, "
                f"当前缓存数: {self.metrics.current_cached_count}, "
                f"平均创建时间: {self.metrics.avg_creation_time:.3f}s"
            )

    def get_pool_stats(self) -> Dict[str, Any]:
        """获取池统计信息"""
        with self._cache_lock:
            return {
                "config": {
                    "enable_session_cache": self.config.enable_session_cache,
                    "enable_component_cache": self.config.enable_component_cache,
                    "session_timeout_minutes": self.config.session_timeout_minutes,
                    "max_cached_sessions": self.config.max_cached_sessions,
                },
                "metrics": {
                    "cache_hit_rate": self.metrics.get_cache_hit_rate(),
                    "reuse_rate": self.metrics.get_reuse_rate(),
                    "cache_hits": self.metrics.cache_hits,
                    "cache_misses": self.metrics.cache_misses,
                    "cache_evictions": self.metrics.cache_evictions,
                    "current_cached_count": self.metrics.current_cached_count,
                    "total_agents_created": self.metrics.total_agents_created,
                    "total_agents_reused": self.metrics.total_agents_reused,
                    "avg_creation_time": self.metrics.avg_creation_time,
                    "concurrent_requests": self.metrics.concurrent_requests,
                    "max_concurrent_requests": self.metrics.max_concurrent_requests,
                },
                "cache_details": [
                    {
                        "cache_key": key,
                        "session_id": info.session_id,
                        "user_id": info.user_id,
                        "created_at": info.created_at,
                        "last_accessed": info.last_accessed,
                        "access_count": info.access_count,
                        "age_seconds": time.time() - info.created_at,
                    }
                    for key, info in self._session_cache.items()
                ]
            }

    def clear_cache(self, session_id: str = None, user_id: str = None):
        """清理缓存"""
        with self._cache_lock:
            if session_id and user_id:
                # 清理特定缓存
                cache_key = f"{user_id}:{session_id}"
                if cache_key in self._session_cache:
                    del self._session_cache[cache_key]
                    self.logger.info(f"清理指定Agent缓存: {cache_key}")
            else:
                # 清理所有缓存
                cleared_count = len(self._session_cache)
                self._session_cache.clear()
                self._component_cache.clear()
                self.logger.info(f"清理所有Agent缓存: {cleared_count} 个")

            self.metrics.current_cached_count = len(self._session_cache)

    def shutdown(self):
        """关闭实例池"""
        self._shutdown_event.set()
        if self._cleanup_thread and self._cleanup_thread.is_alive():
            # 从配置获取线程等待超时时间
            try:
                from backend.config import config_service
                unified_
                thread_timeout = unified_config.get_threshold("performance.timeout.short_operation", 5)
            except Exception:
                thread_timeout = 5
            self._cleanup_thread.join(timeout=thread_timeout)

        self.clear_cache()
        self.logger.info("Agent实例池已关闭")


# 全局实例池
_agent_instance_pool = None
_pool_lock = threading.Lock()


def get_agent_instance_pool() -> AgentInstancePool:
    """获取全局Agent实例池"""
    global _agent_instance_pool

    if _agent_instance_pool is None:
        with _pool_lock:
            if _agent_instance_pool is None:
                _agent_instance_pool = AgentInstancePool()

    return _agent_instance_pool


# 导出主要接口
__all__ = [
    'AgentInstancePool',
    'AgentCacheConfig',
    'CachedAgentInfo',
    'PoolMetrics',
    'get_agent_instance_pool',
]
