"""领域分类AI - 专注于领域分类任务

职责：
- 根据领域描述和对话历史分类用户输入
- 输出标准化的分类结果
- 不处理用户交互和对话引导

输入参数：
- text: 用户输入文本
- domains: 可用领域列表
- conversation_history: 对话历史（可选）

输出格式：
{
    "domain_id": str|null,      # 分类的领域ID，无法确定时为null
    "confidence": float,        # 置信度 0.0-1.0
    "reasoning": str,           # 分类理由
    "status": str              # "completed" 或 "pending"
}
"""


import json
import re
import logging
from typing import Any, Dict, List, Optional, Union, Tuple
import asyncio
from autogen import Agent
from backend.config.logging_config import get_logger
from backend.utils.prompt_loader import PromptLoader
from backend.utils.progress_indicator import ProgressIndicator, ProgressStage

logger = get_logger(__name__)

class DomainClassifierAgent:
    """领域分类器Agent实现"""

    def __init__(self, llm_client: Any, agent_name: str):
        self.llm_client = llm_client
        self.agent_name = agent_name  # 使用agent_name获取模型配置
        self.prompt_loader = PromptLoader()
        self.logger = logging.getLogger(__name__)

    async def classify(self, text: str, domains: List[Dict[str, Any]], conversation_history: List[Dict[str, str]] = None,
                      session_id: str = None, user_id: str = None) -> Dict[str, Any]:
        """基于领域描述和对话历史分类文本"""
        # 创建进度指示器（如果提供了session信息）
        progress = None
        if session_id and user_id:
            progress = ProgressIndicator(session_id, user_id)
            progress.update_progress(ProgressStage.STARTING, 10, "🔍 开始分析需求领域...")

        # 检查输入是否为空
        if not text or text.strip() == "":
            self.logger.warning("收到空输入，使用默认领域")
            return {
                "status": "pending",
                "domain_id": "LY_100",  # 使用LY_100作为默认领域ID
                "confidence": 1.0,
                "reasoning": "用户输入为空，使用默认领域"
            }

        self.logger.info(f"开始分类文本: '{text}'")  # 添加日志记录用户输入

        if progress:
            progress.update_progress(ProgressStage.ANALYZING, 25, "📊 正在加载领域知识库...")

        if not domains:
            self.logger.warning("没有可用的领域信息，默认为通用领域")
            return {
                "status": "completed",
                "domain_id": "general",
                "confidence": 1.0,
                "reasoning": "没有可用的领域信息，默认为通用领域"
            }

        # 创建领域名称到ID和领域ID到名称的映射字典
        domain_name_to_id = {}
        domain_id_to_name = {}

        for domain in domains:
            name = domain.get('name', '').lower()
            domain_id = domain.get('domain_id', str(id(domain)))  # 如果没有domain_id，使用对象id作为临时值
            if name:
                domain_name_to_id[name] = domain_id
                domain_id_to_name[domain_id] = name

        # 构建领域描述部分 - 只使用名称和描述
        domains_section = ""
        for domain in domains:
            domain_id = domain.get('domain_id', '')
            domain_name = domain.get('name', '')
            domain_desc = domain.get('description', '')

            if domain_name:
                # 添加领域名称和描述
                domains_section += f"### {domain_name} ({domain_id})\n"
                if domain_desc:
                    domains_section += f"{domain_desc}\n\n"

        # 构建领域列表
        domains_list = [f"{d.get('name', '')} ({d.get('domain_id', '')})" for d in domains if d.get('name')]

        # 使用PromptLoader加载模板
        prompt = self.prompt_loader.load_prompt(
            "domain_classifier",
            {
                "domains_section": domains_section,
                "user_input": text,
                "conversation_context": self._format_conversation_history(conversation_history) if conversation_history else "",
                "domains_list": ", ".join(domains_list)
            }
        )

        if progress:
            progress.update_progress(ProgressStage.PROCESSING, 50, "🤖 正在进行智能领域分析...")

        # 调用LLM
        # 获取LLM配置参数
        try:
            from backend.config import config_service
            config = config_service.get_llm_config_with_metadata("domain_classifier")
            temperature = config.get("temperature", 0.5)
            max_tokens = config.get("max_tokens", 3000)
        except Exception:
            from backend.config import config_service
            unified_
            temperature = unified_config.get_threshold("llm.default_temperature", 0.5)
            max_tokens = unified_config.get_threshold("llm.default_max_tokens", 3000)

        response = await self.llm_client.call_llm(
            messages=[{"role": "user", "content": prompt}],
            agent_name=self.agent_name,
            temperature=temperature,
            max_tokens=max_tokens
        )

        if progress:
            progress.update_progress(ProgressStage.EXTRACTING, 75, "📋 正在解析分类结果...")

        # 解析响应
        content = response.get("content", "")

        # 尝试从响应中提取JSON
        try:
            # 首先尝试查找JSON代码块
            json_match = re.search(r'```(?:json)?\s*({\s*".*?"\s*:.*?})\s*```', content, re.DOTALL)
            if json_match:
                result = json.loads(json_match.group(1))
            else:
                # 尝试查找花括号包围的JSON
                json_match = re.search(r'({[\s\S]*?})', content)
                if json_match:
                    result = json.loads(json_match.group(1))
                else:
                    # 如果无法找到JSON，尝试匹配领域名称
                    matched_domain = self._match_domain_from_text(content, domains)

                    if matched_domain:
                        domain_id = matched_domain.get('domain_id')
                        domain_name = matched_domain.get('name')
                        result = {
                            "domain_id": domain_id,
                            "confidence": 0.7,
                            "reasoning": f"基于LLM输出匹配领域名称: {domain_name}",
                            "status": "completed"
                        }
                    else:
                        # 如果没有匹配到任何领域，返回pending状态
                        result = {
                            "domain_id": None,
                            "confidence": 0.0,
                            "reasoning": "无法从LLM输出中匹配到任何领域",
                            "status": "pending"
                        }
        except Exception as e:
            # 解析失败，尝试从内容中匹配领域名称
            matched_domain = self._match_domain_from_text(content, domains)

            if matched_domain:
                domain_id = matched_domain.get('domain_id')
                domain_name = matched_domain.get('name')
                result = {
                    "domain_id": domain_id,
                    "confidence": 0.6,
                    "reasoning": f"JSON解析失败: {str(e)}. 匹配领域名称: {domain_name}",
                    "status": "completed"
                }
            else:
                # 如果没有匹配到任何领域，返回pending状态
                result = {
                    "domain_id": None,
                    "confidence": 0.0,
                    "reasoning": f"JSON解析失败: {str(e)}. 无法匹配任何领域",
                    "status": "pending"
                }

        # 确保结果包含所有必要字段并应用分类规则
        if "domain_id" not in result:
            result["domain_id"] = None
        elif result["domain_id"] and isinstance(result["domain_id"], str) and result["domain_id"].lower() in domain_name_to_id:
            # 如果domain_id是领域名称而不是ID，则转换为ID
            result["domain_id"] = domain_name_to_id[result["domain_id"].lower()]

        if "confidence" not in result:
            result["confidence"] = 0.0
        if "reasoning" not in result:
            result["reasoning"] = "未提供理由"

        # 应用分类规则：置信度 >= 0.6 且 domain_id 不为 None 且不为空字符串为 completed，否则为 pending
        confidence = result.get("confidence", 0.0)
        domain_id = result.get("domain_id")
        if confidence >= 0.6 and domain_id not in (None, ""):
            result["status"] = "completed"
        else:
            result["status"] = "pending"
            result["domain_id"] = None  # pending状态下domain_id必须为null

        # 记录分类结果
        self.logger.info(f"领域分类结果: {json.dumps(result, ensure_ascii=False)}")

        if progress:
            domain_name = domain_id_to_name.get(result.get("domain_id"), "未知领域")
            confidence = result.get("confidence", 0.0)
            if result.get("status") == "completed":
                progress.update_progress(ProgressStage.COMPLETED, 100,
                    f"✅ 领域分析完成：{domain_name} (置信度: {confidence:.1f})")
            else:
                progress.update_progress(ProgressStage.COMPLETED, 100,
                    f"⚠️ 领域分析完成，需要更多信息 (置信度: {confidence:.1f})")

        return result

    async def process(self, text: str) -> str:
        """处理输入文本并返回领域分类结果"""
        response = await self.llm_client.call_llm(
            messages=[{"content": text}],
            agent_name=self.agent_name
        )
        return response.get("content", "general")

    async def classify_custom(self, prompt: str, raw_response: bool = False) -> Dict[str, Any]:
        """自定义分类方法，直接使用提供的提示词调用LLM"""
        try:
            # 获取LLM配置参数
            try:
                from backend.config import config_service
                config = config_service.get_llm_config_with_metadata("domain_classifier")
                temperature = config.get("temperature", 0.5)
                max_tokens = config.get("max_tokens", 3000)
            except Exception:
                from backend.config import config_service
                unified_
                temperature = unified_config.get_threshold("llm.default_temperature", 0.5)
                max_tokens = unified_config.get_threshold("llm.default_max_tokens", 3000)

            # 调用LLM
            response = await self.llm_client.call_llm(
                messages=[{"role": "user", "content": prompt}],
                agent_name=self.agent_name,
                temperature=temperature,
                max_tokens=max_tokens
            )

            if raw_response:
                return response

            # 解析响应
            content = response.get("content", "")

            # 尝试从响应中提取JSON
            try:
                # 首先尝试查找JSON代码块
                json_match = re.search(r'```(?:json)?\s*({.*?})\s*```', content, re.DOTALL)
                if json_match:
                    result = json.loads(json_match.group(1))
                else:
                    # 尝试查找花括号包围的JSON
                    json_match = re.search(r'({[\s\S]*?})', content)
                    if json_match:
                        result = json.loads(json_match.group(1))
                    else:
                        # 如果无法找到JSON，返回原始内容
                        result = {"content": content}
            except Exception as e:
                # 解析失败，返回原始内容
                result = {"content": content, "error": str(e)}

            return result

        except Exception as e:
            # 调用失败，返回错误信息
            return {"error": str(e)}

    def _format_conversation_history(self, conversation_history: List[Dict[str, str]]) -> str:
        """格式化对话历史"""
        if not conversation_history:
            return ""

        formatted = []
        for message in conversation_history:
            role = message.get("role", "")
            content = message.get("content", "")
            if role and content:
                formatted.append(f"- {role}: {content}")

        return "\n".join(formatted)

    def _match_domain_from_text(self, text: str, domains: List[Dict[str, Any]]) -> Dict[str, Any]:
        """从文本中匹配最可能的领域"""
        if not text or not domains:
            return None

        text_lower = text.lower()

        # 1. 尝试完全匹配领域名称
        for domain in domains:
            name = domain.get('name', '').lower()
            if name and name in text_lower:
                return domain

        # 2. 尝试匹配领域名称的第一个单词
        for domain in domains:
            name = domain.get('name', '').lower()
            if name:
                first_word = name.split()[0]
                if first_word in text_lower:
                    return domain

        # 3. 尝试匹配领域描述中的关键词
        keywords_to_domain = {}
        for domain in domains:
            desc = domain.get('description', '').lower()
            if desc:
                # 提取描述中的关键词（简单实现，可以根据需要改进）
                keywords = [word for word in desc.split() if len(word) > 3]  # 只考虑长度大于3的词
                for keyword in keywords:
                    if keyword not in keywords_to_domain:
                        keywords_to_domain[keyword] = []
                    keywords_to_domain[keyword].append(domain)

        # 计算每个领域的匹配关键词数量
        domain_scores = {}
        for keyword, matched_domains in keywords_to_domain.items():
            if keyword in text_lower:
                for domain in matched_domains:
                    domain_id = domain.get('domain_id')
                    if domain_id not in domain_scores:
                        domain_scores[domain_id] = 0
                    domain_scores[domain_id] += 1

        # 找出匹配关键词最多的领域
        if domain_scores:
            best_domain_id = max(domain_scores, key=domain_scores.get)
            for domain in domains:
                if domain.get('domain_id') == best_domain_id:
                    return domain

        # 如果以上方法都无法匹配，返回None
        return None

class AutoGenDomainClassifierAgent(Agent):
    """AutoGen兼容的领域分类器Agent"""

    def __init__(self, llm_client: Any = None, agent_name: str = "domain_classifier", agent_description: str | None = None):
        self.agent = DomainClassifierAgent(llm_client, agent_name)
        self._llm_config = None
        self._description = agent_description or "Classifies user input into predefined domains."
        self._name = "AutoGenDomainClassifier"
        self.logger = get_logger(__name__)

    @property
    def name(self) -> str:
        """返回Agent名称"""
        return self._name

    @property
    def description(self) -> str:
        """返回Agent描述"""
        return self._description

    @property
    def llm_config(self) -> Dict[str, Any] | None:
        """返回LLM配置"""
        return self._llm_config

    @llm_config.setter
    def llm_config(self, value: Dict[str, Any] | None) -> None:
        self._llm_config = value

    def send(
        self,
        message: Union[Dict[str, Any], str],
        recipient: "Agent",
        request_reply: Optional[bool] = None,
    ) -> None:
        """向另一个Agent发送消息"""
        try:
            recipient.receive(message, self, request_reply)
        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            raise

    async def a_send(
        self,
        message: Union[Dict[str, Any], str],
        recipient: "Agent",
        request_reply: Optional[bool] = None,
    ) -> None:
        """(异步)向另一个Agent发送消息"""
        try:
            await recipient.a_receive(message, self, request_reply)
        except Exception as e:
            self.logger.error(f"异步发送消息失败: {e}")
            raise

    def receive(
        self,
        message: Union[Dict[str, Any], str],
        sender: "Agent",
        request_reply: Optional[bool] = None,
    ) -> None:
        """接收来自其他Agent的消息"""
        reply = None
        if request_reply is None:
            request_reply = True

        try:
            reply = self.generate_reply([{"content": message, "role": "user"}] if isinstance(message, str) else [message])
            if request_reply and reply is not None:
                self.send(reply, sender)
        except Exception as e:
            self.logger.error(f"处理接收消息失败: {e}")
            if request_reply:
                error_message = f"处理消息时出错: {str(e)}"
                self.send(error_message, sender)

    async def a_receive(
        self,
        message: Union[Dict[str, Any], str],
        sender: "Agent",
        request_reply: Optional[bool] = None,
    ) -> None:
        """(异步)接收来自其他Agent的消息"""
        reply = None
        if request_reply is None:
            request_reply = True

        try:
            reply = await self.a_generate_reply([{"content": message, "role": "user"}] if isinstance(message, str) else [message])
            if request_reply and reply is not None:
                await self.a_send(reply, sender)
        except Exception as e:
            self.logger.error(f"异步处理接收消息失败: {e}")
            if request_reply:
                error_message = f"处理消息时出错: {str(e)}"
                await self.a_send(error_message, sender)

    def generate_reply(
        self,
        messages: Optional[List[Dict[str, Any]]] = None,
        sender: Optional["Agent"] = None,
        **kwargs: Any,
    ) -> Union[str, Dict[str, Any], None]:
        """根据收到的消息生成回复"""
        if not messages:
            return None

        try:
            # 获取所有可用的领域信息
            domains = asyncio.run(self.agent.knowledge_base.get_domains())
            last_message = messages[-1]
            text = last_message.get("content") if isinstance(last_message, dict) else str(last_message)

            # 调用领域分类器进行分类
            result = asyncio.run(self.agent.classify(text, domains))
            return result

        except Exception as e:
            self.logger.error(f"生成回复失败: {e}")
            return {"status": "error", "error": str(e)}

    async def a_generate_reply(
        self,
        messages: Optional[List[Dict[str, Any]]] = None,
        sender: Optional["Agent"] = None,
        **kwargs: Any,
    ) -> Union[str, Dict[str, Any], None]:
        """(异步)根据收到的消息生成回复"""
        if not messages:
            return None

        try:
            # 获取所有可用的领域信息
            domains = await self.agent.knowledge_base.get_domains()
            last_message = messages[-1]
            text = last_message.get("content") if isinstance(last_message, dict) else str(last_message)

            # 调用领域分类器进行分类
            result = await self.agent.classify(text, domains)
            return result

        except Exception as e:
            self.logger.error(f"生成回复失败: {e}")
            return {"status": "error", "error": str(e)}

    @llm_config.setter
    def llm_config(self, value: Dict[str, Any] | None) -> None:
        """设置LLM配置"""
        self._llm_config = value

    async def process_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """处理输入消息,返回分类结果"""
        text = message.get("content", "")
        domains = message.get("domains", [])
        history = message.get("history", [])

        try:
            result = await self.agent.classify(text, domains, history)
            return {"type": "classification_result", "content": result}
        except Exception as e:
            logger.error(f"Domain classification failed: {e}")
            return {"type": "error", "content": str(e)}

    async def step(
        self,
        messages: Optional[List[Dict[str, Any]]] = None,
        sender: Optional[Agent] = None,
        **kwargs
    ) -> Tuple[bool, Optional[Union[str, Dict[str, Any]]]]:
        """执行一个思考和决策步骤"""
        if not messages:
            return False, None

        last_message = messages[-1]
        response = await self.process_message(last_message)
        return True, response

    def can_execute_function(self, function_name: str) -> bool:
        """判断是否可以执行特定函数
        当前DomainClassifierAgent只能执行classify方法
        """
        return function_name == "classify"

    def is_terminal(self, message: Optional[Dict[str, Any]] = None) -> bool:
        """判断当前任务是否已完成
        对于领域分类器来说,每个消息都是独立的,所以总是返回True
        """
        return True
