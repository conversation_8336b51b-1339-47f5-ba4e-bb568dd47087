"""
启动FastAPI服务器的脚本(统一入口)

用于启动需求采集系统的API服务器，使用api/main.py作为统一入口。
提供与前端交互的完整接口，集成所有功能模块。
"""

import os
import sys

# 🔧 修复：在任何导入之前设置ChromaDB环境变量
os.environ['ANONYMIZED_TELEMETRY'] = 'False'
os.environ['CHROMA_TELEMETRY_DISABLED'] = 'True'

import uvicorn

def main():
    """主函数，启动FastAPI服务器"""
    print("启动需求采集系统API服务器(统一入口)...")
    
    # 设置环境变量
    os.environ["PYTHONPATH"] = os.path.dirname(os.path.abspath(__file__))
    
    # 启动FastAPI服务器
    uvicorn.run(
        "backend.api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )

if __name__ == "__main__":
    main()
